import { toCamelCase } from '../stringHelper';

describe('getContextForMoveLegs', () => {
  it.each([
    ['INITIAL_DELIVERY', 'initialDelivery'],
    ['SELF_INITIAL_DELIVERY', 'selfInitialDelivery'],
    ['PICKUP', 'pickup'],
    ['MOVE', 'move'],
    ['VISIT_CONTAINER', 'visitContainer'],
    ['REDELIVERY', 'redelivery'],
    ['WAREHOUSE_TO_WAREHOUSE', 'warehouseToWarehouse'],
    ['SELF_FINAL_PICKUP', 'selfFinalPickup'],
    ['FINAL_PICKUP', 'finalPickup'],
    ['', '']
  ])("format move leg type from '%s' to '%s'", (input, expectedOutput) => {
    expect(toCamelCase(input)).toEqual(expectedOutput);
  });
});
