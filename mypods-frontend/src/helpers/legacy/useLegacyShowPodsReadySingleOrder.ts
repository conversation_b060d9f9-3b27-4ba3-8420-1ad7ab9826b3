import { PODS_READY_SINGLE_ORDER_ENABLED, useFeatureFlags } from '../useFeatureFlags';
import { useEntryPointContext } from '../../context/EntryPointContext';
import {
  OutstandingMothAgreement,
  OutstandingRentalAgreement
} from '../../networkRequests/responseEntities/AuthorizationEntities';

export const useLegacyShowPodsReadySingleOrder = () => {
  const {
    entryPointResult: { outstandingRentalAgreements, outstandingMothAgreements, isFetching }
  } = useEntryPointContext();
  const { isPodsReadySingleOrderEnabled } = useFeatureFlags([PODS_READY_SINGLE_ORDER_ENABLED]);
  const uniqueOrderIds = new Set();

  outstandingRentalAgreements.forEach((doc: OutstandingRentalAgreement) => {
    uniqueOrderIds.add(doc.orderId);
  });

  outstandingMothAgreements.forEach((doc: OutstandingMothAgreement) => {
    uniqueOrderIds.add(doc.orderId);
  });

  const hasExactlyOneOpenOrder = uniqueOrderIds.size === 1;
  const podsReadySingleOrderFlagEnabled = isPodsReadySingleOrderEnabled();
  return {
    isFetching,
    showPodsReadySingleOrder: podsReadySingleOrderFlagEnabled && hasExactlyOneOpenOrder
  };
};
