import React from 'react';
import { Grid } from '@mui/material';
import { Design } from '../../../helpers/Design';
import { IconInCircle } from './IconInCircle';
import { DueDatePill } from './DueDatePill';
import { NextButton } from './NextButton';
import { Title } from './Title';

export interface TaskCardProps {
  Icon: React.ElementType;
  title: string;
  description: string;
  dueDate?: Date;
  isComplete: boolean;
  isMobile: boolean;
  onClick?: () => void;
  testId?: string;
}

export const TaskCard = (props: TaskCardProps) => {
  const { Icon, title, description, dueDate, isComplete, onClick, isMobile, testId } = props;
  const color = isComplete ? Design.Alias.Color.neutral700 : Design.Primitives.Color.Blue.oasis;
  const handleClick = () => {
    if (!isComplete && onClick) {
      onClick();
    }
  };
  const cursor = isComplete ? 'default' : 'pointer';

  return (
    <button
      style={{ ...styles, textAlign: 'start' }}
      type="button"
      onClick={handleClick}
      data-testid={testId}>
      <Grid container>
        <Grid item md={12} lg={10} sx={{ ...styles.itemDescription, cursor }}>
          <IconInCircle Icon={Icon} color={color} />
          <div>
            <Title title={title} isComplete={isComplete} />
            <div style={styles.description}>{description}</div>
            {dueDate && <DueDatePill dueDate={dueDate} color={color} isComplete={isComplete} />}
          </div>
        </Grid>
        <NextButton isComplete={isComplete} isMobile={isMobile} />
      </Grid>
    </button>
  );
};

const styles = {
  display: 'flex',
  borderRadius: '12px',
  borderWidth: '1px',
  borderStyle: 'solid',
  marginTop: '16px',
  padding: '16px 24px 16px 24px',
  width: '100%',
  backgroundColor: 'transparent',
  itemDescription: {
    display: 'flex'
  },
  description: {
    ...Design.Alias.Text.BodyUniversal.Md,
    color: Design.Alias.Color.neutral700,
    marginBottom: '8px'
  }
};
