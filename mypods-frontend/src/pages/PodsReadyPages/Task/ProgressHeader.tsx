import React from 'react';
import { LinearProgress, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Target } from '@phosphor-icons/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';

export interface ProgressHeaderProps {
  percentComplete: number;
}

export const ProgressHeader = (props: ProgressHeaderProps) => {
  const translationKeys = TranslationKeys.TaskPage;
  const { t } = useTranslation();
  return (
    <div style={{ ...styles }}>
      <div style={{ ...styles.header }}>
        <Typography sx={{ ...styles.header.title }}>
          <Target size={32} />
          {t(translationKeys.ProgressBar.TITLE)}
        </Typography>
        <span style={{ ...styles.header.percentage }}>
          {t(TranslationKeys.CommonComponents.PERCENT, { amount: props.percentComplete })}
          <span style={{ ...styles.header.percentage.completed }}>
            {t(translationKeys.ProgressBar.COMPLETED)}
          </span>
        </span>
      </div>
      <LinearProgress
        sx={{ ...styles.progressBar }}
        variant="determinate"
        value={props.percentComplete}
      />
    </div>
  );
};

const styles = {
  width: '100%',
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    title: {
      color: Design.Alias.Color.accent900,
      ...Design.Alias.Text.Heading.Desktop.Md,
      fontWeight: 800,
      svg: {
        position: 'relative',
        top: '8px',
        color: Design.Alias.Color.primary500
      }
    },
    percentage: {
      marginTop: 'auto',
      marginBottom: 'auto',
      ...Design.Alias.Text.BodyUniversal.LgBold,
      completed: {
        marginLeft: '6px',
        color: Design.Alias.Color.neutral700,
        ...Design.Alias.Text.BodyUniversal.MdBold
      }
    }
  },
  progressBar: {
    width: '100%',
    backgroundColor: Design.Alias.Color.neutral200,
    '& .MuiLinearProgress-bar': {
      backgroundColor: Design.Primitives.Color.Blue.oasis
    }
  }
};
