import React from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { CalendarCheck, Bug, Signature } from '@phosphor-icons/react';
import { addDays, subDays } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { PageLayout } from '../../../components/PageLayout';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ProgressHeader } from './ProgressHeader';
import { TaskCard } from './TaskCard';
import { formatToLocale } from '../../../helpers/dateHelpers';
import { theme } from '../../../PodsTheme';
import { OrderStatusCard } from './OrderStatusCard/OrderStatusCard';
import { WelcomeCard } from './WelcomeCard';
import { useGetPodsReadyCustomerOrders } from '../../../networkRequests/queries/podsReady/useGetPodsReadyCustomerOrders';
import { PodsReadyRoutes } from '../../../PodsReadyRoutes';

const translationKeys = TranslationKeys.TaskPage;

export const TaskPage = () => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { customerOrders: orders } = useGetPodsReadyCustomerOrders();

  const firstOrder = orders?.[0];
  const { orderDate } = firstOrder;
  const navigate = useNavigate();
  const showRentalAgreement = () => {
    navigate(PodsReadyRoutes.RENTAL_AGREEMENT);
  };

  return (
    <Grid container {...styles.page}>
      <PageLayout columnsMd={12} columnsLg={8}>
        <Grid container item lg={12} {...styles.headerPanel.mainPanel}>
          <WelcomeCard />
          <OrderStatusCard order={firstOrder} />
        </Grid>
        <Grid container item lg={12}>
          <ProgressHeader percentComplete={45} />
        </Grid>
        <Grid container item lg={12}>
          <TaskCard
            Icon={Signature}
            title={t(translationKeys.RentalAgreement.TITLE)}
            description={t(translationKeys.RentalAgreement.DESCRIPTION)}
            dueDate={addDays(new Date(), 1)}
            isComplete={false}
            isMobile={isMobile}
            onClick={showRentalAgreement}
          />
          <TaskCard
            Icon={Bug}
            title={t(translationKeys.InvasiveSpecies.TITLE)}
            description={t(translationKeys.InvasiveSpecies.DESCRIPTION)}
            dueDate={addDays(new Date(), -2)}
            isComplete={false}
            isMobile={isMobile}
            onClick={() => navigate(PodsReadyRoutes.MOTH_FLY_INSPECTION)}
          />
          {orderDate && (
            <TaskCard
              Icon={CalendarCheck}
              title={t(translationKeys.Order.TITLE)}
              description={t(translationKeys.Order.DESCRIPTION, {
                date: formatToLocale(orderDate)
              })}
              dueDate={subDays(orderDate, -1)}
              isMobile={isMobile}
              isComplete
            />
          )}
        </Grid>
      </PageLayout>
    </Grid>
  );
};

const styles = {
  page: {
    sx: {
      marginTop: Design.Primitives.Spacing.md,
      justifyContent: 'center',
      alignItems: 'flex-start'
    }
  },
  headerPanel: {
    mainPanel: {
      sx: {
        gap: Design.Primitives.Spacing.xs,
        flexWrap: 'nowrap',
        flexDirection: {
          xs: 'column',
          md: 'row'
        },
        marginTop: {
          xs: '12px',
          md: '48px'
        }
      }
    }
  }
};
