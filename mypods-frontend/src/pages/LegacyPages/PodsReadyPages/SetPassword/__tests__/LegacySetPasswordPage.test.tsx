import React from 'react';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { screen, waitFor, within } from '@testing-library/react';
import {
  mockedUseFeatureFlags,
  mockLegacyCreatePodsReadyAccount,
  mockNavigate,
  mockRefreshSession
} from '../../../../../../setupTests';
import {
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../../../testUtils/RenderHelpers';
import { LegacySetPasswordPage } from '../LegacySetPasswordPage';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { PodsReadyCreateAccountStatus } from '../../../../../networkRequests/responseEntities/PodsReadyEntities';
import { AxiosError, AxiosResponse } from 'axios';
import {
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../../../testUtils/MyPodsFactories';

const Tx = TranslationKeys.PodsReady.SetPasswordPage;
const TxAccounts = TranslationKeys.AccountPage.AccountInfo.Password;
const views = {
  input: () => screen.getByLabelText(TxAccounts.Labels.VIEW_PASSWORD),
  saveButton: () => screen.getByRole('button', { name: Tx.BUTTON_TEXT }),
  showHideButton: () => screen.getByRole('button', { name: 'Show/Hide Button' }),
  alert: () => screen.findByRole('alert')
};

describe('LegacySetPasswordPage', () => {
  let user: UserEvent;

  beforeEach(() => {
    user = userEvent.setup();
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
  });

  const renderPage = async () => {
    const result = renderWithLegacyProvidersAndState(<LegacySetPasswordPage />);
    await runPendingPromises();
    return result;
  };

  it('should display the title text & password requirements', async () => {
    await renderPage();
    expect(screen.getByText(Tx.INTRO));
    expect(screen.getByText(Tx.TITLE));
    expect(screen.getByText(Tx.SUBTITLE));

    expect(screen.getByText(TxAccounts.VALIDATION_HEADER)).toBeInTheDocument();
    Object.values(TxAccounts.Rules).forEach((key) =>
      expect(screen.getByText(key)).toBeInTheDocument()
    );
  });

  test.each([
    [0, 0, 4, ''],
    [1, 3, 0, 'P'],
    [2, 2, 0, 'Pa'],
    [3, 1, 0, 'Password'],
    [4, 0, 0, 'Password!']
  ])(
    `should return %d success, %d error, and %d default icons when inputting %s`,
    async (successRuleCount, errorRuleCount, defaultIconCount, inputText) => {
      await renderPage();

      if (inputText != '') {
        await waitFor(async () => {
          await user.type(views.input(), inputText);
        });
      }
      await waitFor(() => {
        expect(screen.queryAllByTestId('password-rule-success')).toHaveLength(successRuleCount);
        expect(screen.queryAllByTestId('password-rule-error')).toHaveLength(errorRuleCount);
        expect(screen.queryAllByTestId('password-rule-default')).toHaveLength(defaultIconCount);
      });
    }
  );

  it('should enable save button when a valid password is entered', async () => {
    await renderPage();

    expect(views.saveButton()).toBeDisabled();

    await waitFor(async () => {
      await userEvent.type(views.input(), 'Password');
    });
    await waitFor(() => {
      expect(views.saveButton()).not.toBeDisabled();
    });
  });

  it('should render the password as text, when the view icon is clicked', async () => {
    await renderPage();

    expect(views.input()).toHaveAttribute('type', 'password');

    await waitFor(async () => {
      await userEvent.click(views.showHideButton());
    });
    expect(views.input()).toHaveAttribute('type', 'text');
  });

  describe('given the user receives a validation error for the new password, they will see an alert upon save', async () => {
    test.each([
      [
        PodsReadyCreateAccountStatus.USERNAME_ALREADY_TAKEN,
        Tx.Notifications.Title.USERNAME_ALREADY_TAKEN
      ],
      [PodsReadyCreateAccountStatus.CUSTOMER_ID_TAKEN, Tx.Notifications.Title.CUSTOMER_ID_TAKEN],
      [PodsReadyCreateAccountStatus.INVALID_PASSWORD, Tx.Notifications.Title.INVALID_PASSWORD]
    ])('when %s response code should show %s message', async (responseCode, displayText) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 400
      } as AxiosResponse;

      mockLegacyCreatePodsReadyAccount.mockRejectedValue(
        new AxiosError('message', '400', undefined, undefined, response)
      );

      await waitFor(() => renderPage());
      await waitFor(async () => await user.type(views.input(), 'Password!'));
      await waitFor(async () => await user.click(views.saveButton()));
      const errorAlert = await views.alert();

      expect(within(errorAlert).getByText(displayText)).toBeInTheDocument();
    });
  });

  it('given an error occurs, displays generic failure', async () => {
    mockLegacyCreatePodsReadyAccount.mockRejectedValue('Something went wrong.');

    await waitFor(() => renderPage());
    await waitFor(async () => await user.type(views.input(), 'Password!'));
    await waitFor(async () => await user.click(views.saveButton()));

    const errorAlert = await views.alert();
    expect(within(errorAlert).getByText(Tx.Notifications.Title.DEFAULT)).toBeInTheDocument();
    expect(within(errorAlert).getByText(Tx.Notifications.Message.DEFAULT)).toBeInTheDocument();
  });

  it('while ispodsreadysingleorderenabled flag is off, should navigate to the success page, when successful', async () => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isPodsReadySingleOrderEnabled: () => false })
    );
    mockLegacyCreatePodsReadyAccount.mockResolvedValue({ status: 200 });

    await waitFor(() => renderPage());
    await waitFor(async () => await user.type(views.input(), 'Password!'));
    await waitFor(async () => await user.click(views.saveButton()));

    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('while ispodsreadysingleorderenabled flag is on, should navigate to the success page, when successful', async () => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isPodsReadySingleOrderEnabled: () => true })
    );
    mockLegacyCreatePodsReadyAccount.mockResolvedValue({ status: 200 });

    await waitFor(() => renderPage());
    await waitFor(async () => await user.type(views.input(), 'Password!'));
    await waitFor(async () => await user.click(views.saveButton()));

    expect(mockNavigate).toHaveBeenCalledWith('/pods-ready/success');
  });
});
