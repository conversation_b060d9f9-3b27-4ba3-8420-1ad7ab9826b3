import React from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Bug, CalendarCheck, Signature } from '@phosphor-icons/react';
import { subDays } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { PageLayout } from '../../../../components/PageLayout';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { formatToLocale } from '../../../../helpers/dateHelpers';
import { theme } from '../../../../PodsTheme';
import { TaskCard } from '../../../PodsReadyPages/Task/TaskCard';
import { ProgressHeader } from '../../../PodsReadyPages/Task/ProgressHeader';
import { WelcomeCard } from '../../../PodsReadyPages/Task/WelcomeCard';
import { OrderStatusCard } from '../../../PodsReadyPages/Task/OrderStatusCard/OrderStatusCard';
import { useLegacyGetPodsReadyCustomerOrders } from '../../../../networkRequests/legacy/queries/podsReady/useLegacyGetPodsReadyCustomerOrders';
import { useLegacyStartPodsReadySession } from '../../../../networkRequests/legacy/queries/podsReady/useLegacyStartPodsReadySession';
import { rentalAgreementDueDate } from '../../../../domain/OrderEntities';
import { useEntryPointContext } from '../../../../context/EntryPointContext';
import { PodsReadyRoutes } from '../../../../PodsReadyRoutes';
import {
  PODS_READY_PASSWORD_ONBOARDING,
  useFeatureFlags
} from '../../../../helpers/useFeatureFlags';
import { findIfEntryPointHasMothForm } from '../../../../networkRequests/responseEntities/AuthorizationEntities';

export const LegacyTaskPage = () => {
  const translationKeys = TranslationKeys.TaskPage;
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t } = useTranslation();
  const { isPodsReadyPasswordOnboardingEnabled } = useFeatureFlags([
    PODS_READY_PASSWORD_ONBOARDING
  ]);
  const { customerOrders: orders } = useLegacyGetPodsReadyCustomerOrders();
  const {
    podsReadySessionClaims: { hasPassword }
  } = useLegacyStartPodsReadySession();
  const { entryPointResult } = useEntryPointContext();
  const { outstandingRentalAgreements, outstandingMothAgreements } = entryPointResult;
  const firstOrder = orders?.[0];
  const { orderDate, orderId } = firstOrder;
  const hasMothForm = findIfEntryPointHasMothForm(entryPointResult, orderId);

  const successRouteBasedOnPasswordOnboarding = () => {
    if (isPodsReadyPasswordOnboardingEnabled()) {
      return hasPassword ? PodsReadyRoutes.SUCCESS : PodsReadyRoutes.SET_PASSWORD;
    }
    return PodsReadyRoutes.SUCCESS;
  };

  const showRentalAgreement = () => {
    const mothFormComplete = outstandingMothAgreements.length === 0;
    const podsReadyRoute = mothFormComplete
      ? successRouteBasedOnPasswordOnboarding()
      : PodsReadyRoutes.TASKS;

    navigate(PodsReadyRoutes.RENTAL_AGREEMENT, {
      state: { onSuccessRoute: podsReadyRoute }
    });
  };

  const handleMothFormCardClick = () => {
    const rentalAgreementComplete = outstandingRentalAgreements.length === 0;
    const podsReadyRoute = rentalAgreementComplete
      ? successRouteBasedOnPasswordOnboarding()
      : PodsReadyRoutes.TASKS;

    navigate(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
      state: { onSuccessRoute: podsReadyRoute }
    });
  };

  return (
    <PageLayout columnsMd={12} columnsLg={8}>
      <Grid container item lg={12} {...styles.headerPanel.mainPanel}>
        <WelcomeCard />
        <OrderStatusCard order={firstOrder} />
      </Grid>
      <Grid container item lg={12}>
        <ProgressHeader percentComplete={45} />
      </Grid>
      <Grid container item lg={12}>
        <TaskCard
          Icon={Signature}
          title={t(translationKeys.RentalAgreement.TITLE)}
          description={t(translationKeys.RentalAgreement.DESCRIPTION)}
          dueDate={rentalAgreementDueDate(firstOrder) ?? new Date()}
          isComplete={outstandingRentalAgreements.length === 0}
          isMobile={isMobile}
          onClick={showRentalAgreement}
          testId="rental-agreement-task-card"
        />
        {hasMothForm && (
          <TaskCard
            Icon={Bug}
            title={t(translationKeys.InvasiveSpecies.TITLE)}
            description={t(translationKeys.InvasiveSpecies.DESCRIPTION)}
            dueDate={rentalAgreementDueDate(firstOrder) ?? new Date()}
            isComplete={outstandingMothAgreements.length === 0}
            isMobile={isMobile}
            onClick={handleMothFormCardClick}
            testId="moth-inspection-task-card"
          />
        )}
        {orderDate && (
          <TaskCard
            Icon={CalendarCheck}
            title={t(translationKeys.Order.TITLE)}
            description={t(translationKeys.Order.DESCRIPTION, {
              date: formatToLocale(orderDate)
            })}
            dueDate={subDays(orderDate, -1)}
            isMobile={isMobile}
            isComplete
          />
        )}
      </Grid>
    </PageLayout>
  );
};

const styles = {
  page: {
    sx: {
      marginTop: Design.Primitives.Spacing.md,
      justifyContent: 'center',
      alignItems: 'flex-start'
    }
  },
  headerPanel: {
    mainPanel: {
      sx: {
        gap: Design.Primitives.Spacing.xs,
        flexWrap: 'nowrap',
        flexDirection: {
          xs: 'column',
          md: 'row'
        },
        marginTop: {
          xs: '12px',
          md: '48px'
        }
      }
    }
  }
};
