import React from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Grid,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useBeforeUnload, useLocation, useNavigate } from 'react-router-dom';
import { PageLayout } from '../../components/PageLayout';
import {
  BillingInvoice,
  MonthlyBillingStatement
} from '../../networkRequests/responseEntities/BillingEntities';
import { RightChevronIcon } from '../../components/icons/RightChevronIcon';
import { BillingCard, BillingCardData } from './components/BillingCard';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { AccountBalanceCard } from './components/AccountBalanceCard';
import { ROUTES } from '../../Routes';
import { theme } from '../../PodsTheme';
import { PrimaryPaymentMethodCard } from './components/PrimaryPaymentMethodCard';
import { HeaderCardWrapper } from './components/HeaderCardWrapper';
import { AcornFinancing } from './components/AcornFinancing';
import { PodsAlert, PodsAlertProps } from '../../components/alert/PodsAlert';
import { useRefreshSession } from '../../networkRequests/queries/useRefreshSession';
import { CustomerType } from '../../networkRequests/responseEntities/CustomerEntities';
import { StatementsSection } from './components/StatementsSection';
import { PaymentHistorySection } from './components/PaymentHistorySection';
import { useGetBillingInformation } from '../../networkRequests/queries/useGetBillingInformation';
import { Design } from '../../helpers/Design';
import { usePreloadData } from '../../networkRequests/usePreloadData';

export type BillingPageLocationState = {
  alertProps?: PodsAlertProps;
};

// TODO: Add this type of mapping to our APITypes to DomainTypes mapper.
const convertBillingInvoiceToBillingCardData = (invoice: BillingInvoice): BillingCardData =>
  ({
    amount: invoice.isPaid ? invoice.totalDue : invoice.balanceDue,
    totalAmount: invoice.totalDue,
    currencyType: invoice.currencyType!,
    date: invoice.dueDate!,
    id: invoice.invoiceNumber!,
    documentId: invoice.documentId!,
    invoiceNumber: invoice.invoiceNumber!,
    type: invoice.isPaid ? 'paid-invoice' : 'unpaid-invoice',
    isPoet: invoice.isPoet
  }) as BillingCardData;

export const BillingPage = () => {
  const { t: translate } = useTranslation();
  const { billingInformation, error: billingError } = useGetBillingInformation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const location = useLocation();
  const locationState: BillingPageLocationState | null = location.state;
  const navigate = useNavigate();
  const {
    sessionClaims: { type }
  } = useRefreshSession();

  // On page refresh, clear location state
  useBeforeUnload(
    React.useCallback(() => {
      if (locationState?.alertProps) {
        navigate(ROUTES.BILLING);
      }
    }, [])
  );
  usePreloadData();

  if (billingError) return <pre>{JSON.stringify(billingError, null, 2)}</pre>;

  // TODO: Convert to dates when we get them from the backend? -eh
  const invoicesByOldestDate = (a: BillingInvoice, b: BillingInvoice) =>
    Date.parse(a.dueDate!!) - Date.parse(b.dueDate!!);

  const invoicesByLatestDate = (a: BillingInvoice, b: BillingInvoice) =>
    Date.parse(b.dueDate!!) - Date.parse(a.dueDate!!);

  const statementsByLatestDate = (a: MonthlyBillingStatement, b: MonthlyBillingStatement) =>
    Date.parse(b.createdDateTime!!) - Date.parse(a.createdDateTime!!);

  const upcomingPayments = billingInformation.invoices
    ?.filter((invoice) => !invoice.isPaid)
    .sort(invoicesByOldestDate);
  const paymentHistory = billingInformation.invoices
    ?.filter((invoice) => invoice.isPaid)
    .sort(invoicesByLatestDate);
  const sortedStatements = billingInformation.monthlyStatements?.sort(statementsByLatestDate);
  const paymentHistoryData = { paymentHistory };
  const statementData = { sortedStatements };
  const isResidentialCustomer: boolean = type === CustomerType.RESIDENTIAL;

  return (
    <PageLayout columnsLg={6}>
      <Grid data-testid="billing-page" {...styles.page}>
        <Typography variant="h1">{translate(TranslationKeys.BillingPage.HEADER)}</Typography>
        {locationState?.alertProps && <PodsAlert {...locationState.alertProps} />}
        <Grid
          container
          spacing={2}
          alignItems="stretch"
          flexDirection={isMobile ? 'column' : 'row'}>
          <Grid item sm={7} flexDirection="column" display="flex">
            <AccountBalanceCard />
          </Grid>
          <Grid item sm={5} flexDirection="column" display="flex">
            <HeaderCardWrapper>
              <PrimaryPaymentMethodCard />
            </HeaderCardWrapper>
          </Grid>
        </Grid>
        <AcornFinancing />
        <Grid data-testid="upcoming-payments-section">
          <Accordion disableGutters defaultExpanded {...styles.accordion}>
            <AccordionSummary
              {...styles.accordionSummary}
              expandIcon={<RightChevronIcon {...styles.rightChevronIcon} />}>
              <Typography variant="h4" {...styles.accordionTitle}>
                {translate(TranslationKeys.BillingPage.UpcomingPayments.HEADER)}
              </Typography>
            </AccordionSummary>
            <AccordionDetails {...styles.accordionDetails}>
              <Grid container data-testid="upcoming-payments-content" gap="8px">
                {upcomingPayments?.map((payment) => (
                  <BillingCard
                    key={payment.invoiceNumber}
                    {...convertBillingInvoiceToBillingCardData(payment)}
                  />
                ))}
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Grid>

        <PaymentHistorySection {...paymentHistoryData} styles={styles} />
        {!isResidentialCustomer && <StatementsSection {...statementData} styles={styles} />}
      </Grid>
      {isResidentialCustomer && (
        <PodsAlert
          title={translate(TranslationKeys.BillingPage.StatementInfoAlert.TITLE)}
          description={translate(TranslationKeys.BillingPage.StatementInfoAlert.DESCRIPTION)}
        />
      )}
    </PageLayout>
  );
};

const styles = {
  page: {
    sx: {
      gap: '32px',
      display: 'flex',
      flexDirection: 'column'
    }
  },
  accordion: {
    sx: { boxShadow: 'none' }
  },
  accordionSummary: {
    sx: {
      minHeight: 'fit-content',
      flexDirection: 'row-reverse',
      '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
        transform: 'rotate(90deg)'
      },
      '& .MuiAccordionSummary-content': {
        margin: 0
      },
      gap: Design.Primitives.Spacing.xxs
    }
  },
  accordionTitle: {
    sx: {
      color: Design.Alias.Color.accent900,
      flex: 1
    }
  },
  accordionDetails: {
    sx: {
      paddingLeft: 0,
      paddingRight: 0
    }
  },
  accordionHeader: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  },
  rightChevronIcon: {
    sx: {
      width: '16px',
      height: '16px'
    },
    style: {
      color: Design.Alias.Color.accent900
    }
  }
};
