import React from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, useMediaQuery } from '@mui/material';
import { PodsAlert } from '../../components/alert/PodsAlert';
import { HOME_PAGE_ALERT_ENABLED } from '../../helpers/useFeatureFlags';
import { theme } from '../../PodsTheme';

export const DynamicHomeAlert = () => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const style = styles(isMobile);

  const alert = (
    <div style={style}>
      <PodsAlert
        title={t(`${HOME_PAGE_ALERT_ENABLED}.title`)}
        description={t(`${HOME_PAGE_ALERT_ENABLED}.body`)}
      />
    </div>
  );

  if (isMobile) {
    return alert;
  }
  return (
    <Grid container item lg={8} {...style.main}>
      {alert}
    </Grid>
  );
};

const styles = (isMobile: boolean) => ({
  marginTop: isMobile ? '-22px' : '10px',
  marginBottom: isMobile ? '32px' : '48px',
  maxWidth: '640px',
  main: {
    sx: {
      maxHeight: '166px'
    }
  }
});
