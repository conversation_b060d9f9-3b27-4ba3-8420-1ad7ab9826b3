import { screen, waitFor } from '@testing-library/react';
import { VerifyChallengeErrorStatus } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { mockRefreshSession, mockVerifyChallengeCode } from '../../../../../setupTests';
import { renderWithPoetProvidersAndState } from '../../../../testUtils/RenderHelpers';
import { createRefreshSessionClaims } from '../../../../testUtils/MyPodsFactories';
import { EmailChallengeOTPDialog } from '../ChallengeEmailField/EmailChallengeOTPDialog';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { RefreshSessionClaims } from '../../../../networkRequests/responseEntities/AuthorizationEntities';

// -- constants --
const Tx = TranslationKeys.AccountPage.AccountInfo.Email.OtpVerification.Dialog;

describe('EmailChallengeOTPDialog.tsx', () => {
  let sessionClaims: RefreshSessionClaims;
  const customerId = '*********';
  const emailAddress = '<EMAIL>';

  let newEmail = '<EMAIL>';

  beforeEach(() => {
    sessionClaims = createRefreshSessionClaims({ customerId: customerId, username: emailAddress });

    mockRefreshSession.mockResolvedValue(sessionClaims);
  });

  async function renderOTPCodeDialog() {
    const result = renderWithPoetProvidersAndState(
      <EmailChallengeOTPDialog
        isOpen
        newEmail={newEmail}
        onSuccess={vi.fn()}
        onError={vi.fn()}
        onCloseClicked={vi.fn()}
      />
    );

    await waitFor(() => screen.getByText(Tx.TITLE));
    return result;
  }

  it('shows the the title, inputs, and copy of the dialog', async () => {
    await renderOTPCodeDialog();

    expect(screen.getByText(Tx.TITLE)).toBeVisible();
    expect(screen.getByText(Tx.SUBTITLE)).toBeVisible();
    expect(screen.getByText(newEmail)).toBeVisible();
  });

  it.skip('should display helper text and error styles, if challenge code fails', async () => {
    mockVerifyChallengeCode.mockRejectedValue(VerifyChallengeErrorStatus.INVALID_CODE);
    await renderOTPCodeDialog();
    // not able to interact with or stub out the otp input component
    expect(screen.getByText(Tx.SUBTITLE)).toBeVisible();
    expect(screen.getByText(Tx.HELPER_TEXT)).toBeVisible();
  });
});
