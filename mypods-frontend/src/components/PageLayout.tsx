import React, { ReactNode } from 'react';
import { Grid } from '@mui/material';
import { theme } from '../PodsTheme';

interface LayoutProps {
  children: ReactNode;
  columnsMd?: number;
  columnsLg: number;
  isHeader?: boolean;
}

export const PageLayout: React.FC<LayoutProps> = ({
  children,
  columnsMd = 10,
  columnsLg,
  isHeader = false
}: LayoutProps) => (
  <Grid container justifyContent="center" sx={{ maxWidth: '1440px', height: '100%' }}>
    <Grid
      item
      xs={12}
      sm={12}
      md={columnsMd}
      lg={columnsLg}
      sx={{
        height: '100%',
        [theme.breakpoints.between('xs', 'sm')]: {
          paddingX: isHeader ? '0px' : '12px' // mobile, tablet
        },
        [theme.breakpoints.between('sm', 'md')]: {
          paddingX: '12px' // mobile, tablet
        }
      }}>
      {children}
    </Grid>
  </Grid>
);
