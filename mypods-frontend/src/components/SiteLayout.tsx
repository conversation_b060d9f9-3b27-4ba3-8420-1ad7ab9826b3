import React, { ReactNode } from 'react';
import { Grid } from '@mui/material';
import { NavigationBar } from './NavigationBar/NavigationBar';
import { Footer } from './Footer/Footer';
import { Design } from '../helpers/Design';

interface LayoutProps {
  children: ReactNode;
  showHeaderFooter?: boolean;
  globalBanners?: ReactNode;
}

export const SiteLayout: React.FC<LayoutProps> = ({
  children,
  showHeaderFooter = true,
  globalBanners
}: LayoutProps) => (
  <Grid {...styles.container}>
    {showHeaderFooter && <NavigationBar />}
    <Grid {...styles.belowHeaderContent}>
      {globalBanners}
      <Grid {...styles.pageLayoutContent}>{children}</Grid>
      {showHeaderFooter && <Footer />}
    </Grid>
  </Grid>
);

// -- styles --
const styles = {
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: '100%'
    }
  },
  belowHeaderContent: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      overflowY: 'auto',
      width: '100%',
      alignItems: 'center'
    }
  },
  pageLayoutContent: {
    sx: {
      paddingTop: {
        xs: Design.Primitives.Spacing.md,
        md: Design.Primitives.Spacing.lgPlus
      },
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      flex: 1
    }
  }
};
