import { Grid, Link, Typography, useMediaQuery } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ArrowSquareOut } from '@phosphor-icons/react';
import { Design } from '../../../helpers/Design';
import { PageLayout } from '../../PageLayout';
import { theme } from '../../../PodsTheme';
import { OutstandingRentalAgreement } from '../../../networkRequests/responseEntities/AuthorizationEntities';
import { OrderDocument } from '../../../domain/DocumentEntities';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { AddSignatureButton } from '../../buttons/AddSignature';
import { AgreeButton } from './AgreeButton';
import { BackHyperLink } from '../../buttons/BackHyperLink';
import { PodsReadyRoutes } from '../../../PodsReadyRoutes';
import { Customer } from '../../../networkRequests/responseEntities/CustomerEntities';

export interface ESignRentalAgreementViewerProps {
  onAccept: () => void;
  pdfUrl: string;
  outstandingRentalAgreement: OutstandingRentalAgreement | OrderDocument;
  totalRentalAgreementsToSign: number;
  numberOfCurrentAgreement: number;
  isProcessing: boolean;
  podsReadyTaskPage: boolean;
  customer: Customer;
}

const translationKeys = TranslationKeys.Onboarding.SignRentalAgreements;

export const ESignRentalAgreementViewer = ({
  onAccept,
  pdfUrl,
  outstandingRentalAgreement,
  totalRentalAgreementsToSign,
  numberOfCurrentAgreement,
  isProcessing,
  podsReadyTaskPage,
  customer
}: ESignRentalAgreementViewerProps) => {
  const { t } = useTranslation();
  const [isSigned, setIsSigned] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    setIsSigned(false);
  }, [numberOfCurrentAgreement]);

  return (
    <Grid
      sx={{
        paddingX: isMobile ? '12px' : '104px',
        paddingTop: Design.Primitives.Spacing.lgPlus,
        overflowY: 'auto',
        justifyContent: 'center',
        display: 'flex',
        height: 'calc(100% - 80px)'
      }}>
      <PageLayout columnsMd={12} columnsLg={8}>
        {podsReadyTaskPage && (
          <BackHyperLink
            route={PodsReadyRoutes.TASKS}
            label={t(TranslationKeys.Onboarding.RETURN_TO_TASK_BUTTON)}
            style={{ ...styles.backlink }}
          />
        )}
        <div style={isMobile ? { marginTop: '16px' } : styles.agreementPanel}>
          <Grid container {...styles.mainPanel}>
            {totalRentalAgreementsToSign > 1 && (
              <Typography sx={{ ...Design.Alias.Text.BodyUniversal.LgBold }}>
                {t(TranslationKeys.CommonComponents.PROGRESS_COUNTER, {
                  current: numberOfCurrentAgreement,
                  total: totalRentalAgreementsToSign
                })}
              </Typography>
            )}
            {!podsReadyTaskPage && (
              <Typography sx={{ ...Design.Alias.Text.BodyUniversal.MdBold }}>
                {t(translationKeys.ORDER_NUMBER, {
                  orderNumber: outstandingRentalAgreement.orderId
                })}
              </Typography>
            )}
            <Typography variant="h1">{t(translationKeys.HEADER)}</Typography>
            <Typography sx={{ ...Design.Alias.Text.BodyUniversal.Sm }}>
              {t(translationKeys.SUBTITLE)}
            </Typography>
            <Link
              href={pdfUrl}
              target="blank"
              underline="none"
              sx={{
                ...Design.Alias.Text.BodyUniversal.Sm,
                color: Design.Primitives.Color.Blue.oasis
              }}>
              {t(translationKeys.VIEW_RENT_AGREEMENT)}
              <span style={{ position: 'relative', top: '4px', left: '4px' }}>
                <ArrowSquareOut size={21} />
              </span>
            </Link>
          </Grid>
          <Grid container {...styles.buttons}>
            <AddSignatureButton
              firstName={customer.firstName}
              lastName={customer.lastName}
              customerId={customer.id}
              isSigned={isSigned}
              handleSignClicked={() => setIsSigned(true)}
            />
            <AgreeButton
              onAccept={onAccept}
              isMobile={isMobile}
              isSigned={isSigned}
              isProcessing={isProcessing}
            />
          </Grid>
        </div>
      </PageLayout>
    </Grid>
  );
};

const styles = {
  agreementPanel: {
    marginTop: 'calc(50% - 200px)',
    borderRadius: '8px',
    borderStyle: 'solid',
    borderWidth: '1px',
    borderColor: Design.Alias.Color.neutral300,
    backgroundColor: Design.Alias.Color.neutral100,
    boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)',
    padding: '32px'
  },
  backlink: {
    display: 'flex',
    alignItems: 'center',
    textDecoration: 'none',
    color: 'inherit',
    position: 'relative',
    left: '-7px',
    marginBottom: '16px'
  },
  mainPanel: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.sm,
      maxWidth: '650px'
    }
  },
  buttons: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: '2rem',
      marginTop: '2rem'
    }
  }
};
