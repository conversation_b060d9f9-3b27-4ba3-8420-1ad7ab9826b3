import React, { ReactNode } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { theme } from '../../PodsTheme';
import { Design } from '../../helpers/Design';
import { LegacyNavigationBar } from './NavigationBar/LegacyNavigationBar';
import { LegacyFooter } from './Footer/LegacyFooter';

interface LayoutProps {
  children: ReactNode;
  showHeaderFooter?: boolean;
  globalBanners?: ReactNode;
}

export const LegacySiteLayout: React.FC<LayoutProps> = ({
  children,
  showHeaderFooter = true,
  globalBanners
}: LayoutProps) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = siteLayoutStyles(isMobile);

  return (
    <Grid {...styles.container}>
      {showHeaderFooter && <LegacyNavigationBar />}
      <Grid {...styles.belowHeaderContent}>
        {globalBanners}
        <Grid {...styles.pageLayoutContent}>{children}</Grid>
        {showHeaderFooter && <LegacyFooter />}
      </Grid>
    </Grid>
  );
};

// -- styles --
const siteLayoutStyles = (isMobile: boolean) => ({
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: '100%'
    }
  },
  belowHeaderContent: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      overflowY: 'auto',
      width: '100%',
      alignItems: 'center'
    }
  },
  pageLayoutContent: {
    sx: {
      paddingTop: isMobile ? Design.Primitives.Spacing.md : Design.Primitives.Spacing.lgPlus,
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      flex: 1
    }
  }
});
