import React, { useState } from 'react';
import { AppBar, Box, Chip, Grid, Toolbar, useMediaQuery } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { theme } from '../../../PodsTheme';
import { Design } from '../../../helpers/Design';
import { ROUTES } from '../../../Routes';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { PageLayout } from '../../PageLayout';
import { isNonProdEnv } from '../../../pages/DebugPage/DebugPage';
import { NavigationItem } from '../../ComponentTypes';
import { PhoneIcon } from '../../icons/PhoneIcon';
import { MenuToggleIcon } from '../../NavigationBar/MenuToggleIcon';
import { DesktopMenuBar } from '../../NavigationBar/DesktopMenuBar';
import { NavigationDropdown } from '../../NavigationBar/AccountMenu/NavigationDropdown';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { LegacyAcornFinancing } from '../../../pages/LegacyPages/BillingPage/components/LegacyAcornFinancing';
import { MobileMenuDrawer } from '../../NavigationBar/MobileMenuDrawer';

export const LegacyNavigationBar = () => {
  const { t: translate } = useTranslation();
  const { customer } = useLegacyGetCustomer();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = navBarStyles(isMobile);

  const navItems = (search: string): NavigationItem[] => {
    let items = [
      {
        title: translate(TranslationKeys.Navigation.HOME),
        path: ROUTES.HOME + search
      },
      {
        title: translate(TranslationKeys.Navigation.BILLING),
        path: ROUTES.BILLING + search
      },
      {
        title: translate(TranslationKeys.DocumentsPage.HEADER),
        path: ROUTES.DOCUMENT + search
      }
    ];
    if (isNonProdEnv()) {
      items = [
        ...items,
        {
          title: 'Debug',
          path: ROUTES.DEBUG + location.search
        }
      ];
    }
    return items;
  };
  const navigationItems = navItems(location.search);

  const onMenuToggleClicked = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <AppBar position="relative" {...styles.appBar}>
      <PageLayout columnsLg={9} isHeader>
        <Toolbar disableGutters sx={{ maxWidth: '1440px', width: '100%' }}>
          {isMobile && (
            <MenuToggleIcon onClickToggle={onMenuToggleClicked} isMenuOpen={isMobileMenuOpen} />
          )}
          <Box>
            <img
              src="https://sagrseuspodsrbf01.blob.core.windows.net/rbf/pods-secondary-logo-rgb-1.webp"
              alt="pods-logo"
              {...styles.image}
            />
          </Box>
          {!isMobile && <DesktopMenuBar items={navigationItems} />}
          {!isMobile && (
            <Chip
              icon={<PhoneIcon {...styles.supportPhoneIcon} />}
              label={translate(TranslationKeys.Navigation.SUPPORT_PHONE_NUMBER)}
              size="medium"
              variant="filled"
              {...styles.supportChip}
            />
          )}
          {!isMobile && <NavigationDropdown customer={customer} />}
        </Toolbar>

        <MobileMenuDrawer
          isOpen={isMobileMenuOpen}
          setIsMobileMenuOpen={setIsMobileMenuOpen}
          customerEmail={customer.email?.address}
        />
      </PageLayout>
      {isMobile && location.pathname === ROUTES.HOME && (
        <Grid container sx={{ display: isMobileMenuOpen ? 'none' : 'block' }}>
          <LegacyAcornFinancing widgetKind="banner" />
        </Grid>
      )}
    </AppBar>
  );
};

const navBarStyles = (isMobile: boolean) => ({
  appBar: {
    sx: {
      backgroundColor: Design.Primitives.Color.NeutralLight.white,
      zIndex: 998,
      boxShadow: isMobile
        ? '0px 0px 16px 0px rgba(0, 0, 0, 0.16)'
        : '0px 1px 3px 0px rgba(0, 0, 0, 0.50)',
      alignItems: 'center'
    }
  },
  image: {
    style: {
      height: '22px',
      display: 'flex',
      alignItems: 'center'
    }
  },
  supportPhoneIcon: {
    style: {
      fontSize: '1rem',
      color: Design.Alias.Color.secondary500,
      height: '15px',
      width: '15px',
      marginLeft: '12px',
      marginRight: '0'
    }
  },
  supportChip: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.SmBold,
      lineHeight: Design.Primitives.Spacing.sm,
      fontWeight: 600,
      color: Design.Alias.Color.secondary500,
      backgroundColor: Design.Alias.Color.secondary100,
      marginRight: '36px',
      '.MuiChip-label': {
        lineHeight: Design.Primitives.Spacing.sm,
        paddingY: '6px',
        paddingRight: '12px',
        paddingLeft: '6px'
      }
    }
  }
});
