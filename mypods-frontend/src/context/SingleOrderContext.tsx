import React, { createContext, FC, ReactNode, useContext, useMemo, useState } from 'react';
import { Order, OrderType } from '../domain/OrderEntities';

interface SingleOrderContextProps {
  state: Pick<ISingleOrderContextState, 'order'>;
  children: ReactNode;
}

export type MoveLegScheduling = {
  currentlySelectedMoveLegId: string | null;
  editMoveLegScheduling: (_: string) => void;
  stopMoveLegScheduling: () => void;
  isSaving: boolean;
  setIsSaving: (_: boolean) => void;
  isCancelling: boolean;
  setIsCancelling: (_: boolean) => void;
};

export interface ISingleOrderContextState {
  order: Order;
  moveLegScheduling: MoveLegScheduling;
}

const initialState: ISingleOrderContextState = {
  order: {
    orderId: '',
    quoteId: 0,
    orderType: OrderType.LOCAL,
    containers: [],
    orderDate: undefined,
    price: 0,
    initialDeliveryPlacementIsReviewed: true
  },
  moveLegScheduling: {
    currentlySelectedMoveLegId: null,
    editMoveLegScheduling: (_: string) => {},
    stopMoveLegScheduling: () => {},
    isSaving: false,
    setIsSaving: (_: boolean) => {},
    isCancelling: false,
    setIsCancelling: (_: boolean) => {}
  }
};

export const SingleOrderContext = createContext<ISingleOrderContextState>(initialState);

export default function useSingleOrderContext() {
  return useContext(SingleOrderContext);
}

export const SingleOrderProvider: FC<SingleOrderContextProps> = ({ state, children }) => {
  const [currentlySelectedMoveLegId, setCurrentlySelectedMoveLegId] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isCancelling, setIsCancelling] = useState<boolean>(false);

  const handleSchedulingMoveLeg = (moveLegId: string) => {
    setCurrentlySelectedMoveLegId(moveLegId);
    setIsCancelling(false);
  };

  const handleStopScheduling = () => {
    setCurrentlySelectedMoveLegId(null);
    setIsCancelling(false);
  };

  const value = useMemo(
    () => ({
      ...state,
      moveLegScheduling: {
        currentlySelectedMoveLegId,
        editMoveLegScheduling: handleSchedulingMoveLeg,
        stopMoveLegScheduling: handleStopScheduling,
        isSaving,
        setIsSaving,
        isCancelling,
        setIsCancelling
      }
    }),
    [state, currentlySelectedMoveLegId, isSaving]
  );

  return <SingleOrderContext.Provider value={value}>{children}</SingleOrderContext.Provider>;
};
