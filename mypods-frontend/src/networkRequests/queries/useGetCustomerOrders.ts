import { useSuspenseQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { orderApiToDomain } from '../responseEntities/OrderAPIEntities';
import { QueryCacheKeys } from '../QueryCacheKeys';

export const useGetCustomerOrders = () => {
  const { getCustomerOrders } = useMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.CUSTOMER_ORDERS_CACHE_KEY],
    queryFn: () => getCustomerOrders()
  });

  return {
    ...queryResult,
    customerOrders: queryResult.data?.map((order) => orderApiToDomain(order))
  };
};
