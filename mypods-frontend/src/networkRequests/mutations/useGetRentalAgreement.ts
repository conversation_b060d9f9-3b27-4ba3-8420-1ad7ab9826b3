import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useGetRentalAgreement = () => {
  const { getRentalAgreement } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.GET_RENTAL_AGREEMENT_KEY],
    mutationFn: (orderId: string) => getRentalAgreement(orderId)
  });

  return { ...mutationResult };
};
