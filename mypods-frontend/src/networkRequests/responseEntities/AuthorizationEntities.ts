import isEmpty from 'lodash/isEmpty';
import { CustomerAddress, CustomerType } from './CustomerEntities';
import { orderHasSignedMothAgreement } from '../../helpers/storageHelpers';

export type EntryPointResult = {
  outstandingRentalAgreements: OutstandingRentalAgreement[];
  outstandingMothAgreements: OutstandingMothAgreement[];
  outstandingFnpsAgreements: OutstandingFnpsAgreement[];
  hasWeightTickets: boolean;
  maintenanceModeEnabled: boolean;
  acornFinancingEnabled: boolean;
  isFetching: boolean;
};

export const findIfEntryPointHasMothForm = (
  entryPointData: EntryPointResult,
  orderId: string
): boolean => {
  if (isEmpty(entryPointData.outstandingMothAgreements)) {
    return orderHasSignedMothAgreement(orderId);
  }
  return true;
};

export type RentalAgreementType = 'LOCAL' | 'IF';

export type OutstandingRentalAgreement = {
  orderId: string;
  companyCode: string;
  identity: string;
  agreementType: RentalAgreementType;
  id?: string;
};

export type OutstandingMothAgreement = {
  orderId: string;
};

export type OutstandingFnpsAgreement = {
  customerId: string;
  orderId: string;
  moveId: string;
  isSigned: boolean;
  address: CustomerAddress;
};

export type RefreshSessionClaims = {
  customerId: string;
  firstName: string;
  lastName: string;
  username: string;
  type: CustomerType;
  ownedCuids: string[];
  trackingUuid: string;
};

export const formatFullName = (customer: RefreshSessionClaims) =>
  `${customer.firstName ?? 'Web'} ${customer.lastName ?? 'Customer'}`;
