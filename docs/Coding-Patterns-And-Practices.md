<h1>Coding Patterns And Practices</h1>
This document captures the decision record of any notable 
patterns, practices, or principles and captures their intent. 


<h2>Kotlin / Spring</h2>

* Separate classes responsible for Api / Business Logic (Domain) / and Data Access (Client and Repository) into separate
  files.
    * ApiResponse objects kept separated from domain Result or Domain objects
    * Service interfaces to be used by the Domain classes so implementations could be swapped out
        * Helps produce highly modular, cohesive, and flexible code that can be orchestrated depending on the needs of
          the api endpoint
* Treat code for a domain as package private, only publicly exposing as little as necessary.
    * Encapsulate the logic for these domain behind public interfaces,
      so that code that relies on the implementation only needs to know how to use it,
      not how it works
    * Avoid Leaky Abstractions in Controller / Client / and Service (Domain) Layers

<h2>Typescript / React</h2>

<h2>Testing</h2>

* Remove duplicated test setup
    * Duplicated test setup distracts from the thing being tested and is harder to maintain
* Setup a happy path in tests and override the edge cases
    * Most of the time there is a happy path behavior, that most tests will share the same setup for,
      the tests are simpler when each edge case only overrides the code relevant to that test case

-- To Be Discussed

* ApiResponse: Agree on the interface that all apis will return, and agree that all response objects will implement it
* How should we structure our Api Endpoints for mypods
* Translation message (English / French) should be the responsibilty of the front end
    * Front end must always translate, backend should return state and status codes
* React components should never use inline styling, use a framework or json object and spread the css values to the
  component
    * Inline styles can obfuscate and distract from the structure of the dom