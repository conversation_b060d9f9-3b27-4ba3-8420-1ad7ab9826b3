parameters:
  - name: serviceConnection
    type: string
  - name: targetCluster
    type: string
  - name: targetResourceGroup
    type: string

steps:
  - task: AzureCLI@2
    displayName: 'Install kubelogin and log into ${{parameters.targetCluster}}'
    inputs:
      azureSubscription: ${{parameters.serviceConnection}}
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        set -e
        
        wget https://github.com/Azure/kubelogin/releases/download/v0.0.10/kubelogin-linux-amd64.zip
        unzip kubelogin-linux-amd64.zip
        sudo cp bin/linux_amd64/kubelogin /usr/bin
        
        export KUBECONFIG=$(System.DefaultWorkingDirectory)/.kubeconfig-${{parameters.targetCluster}}
        
        # Fails if it doesn't exist
        touch .kubeconfig-${{parameters.targetCluster}}
        chmod 600 .kubeconfig-${{parameters.targetCluster}}
        
        # Populate kubeconfig
        az aks get-credentials \
        --resource-group ${{parameters.targetResourceGroup}} \
        --name ${{parameters.targetCluster}} \
        --overwrite-existing \
        --file .kubeconfig-${{parameters.targetCluster}} \
        --format azure
        
        # Pass kubeconfig to kubelogin to access k8s API
        kubelogin convert-kubeconfig -l azurecli
        
        # Ensure the kubeconfig path is available to subsequent tasks as an environment variable
        echo "##vso[task.setvariable variable=KUBECONFIG]${KUBECONFIG}"
