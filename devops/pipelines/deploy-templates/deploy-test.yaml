parameters:
  - name: devopsFolderPath
    type: string
  - name: chartName
    type: string

jobs:
  - deployment: Deploy_Test
    displayName: 'Deploy Test'
    environment: 'mypods-test'
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: self
            - template: ../step-templates/kubelogin.yaml
              parameters:
                serviceConnection: SC-PODS-NP
                targetCluster: AKS-EUS-TE-01
                targetResourceGroup: RG-INFRA-TE

            - template: ../step-templates/helm-deploy.yaml
              parameters:
                serviceConnection: SC-PODS-NP
                targetCluster: AKS-EUS-TE-01
                environment: test
                namespace: mypods
                chartName: ${{ parameters.chartName }}
                devopsFolderPath: ${{ parameters.devopsFolderPath }}