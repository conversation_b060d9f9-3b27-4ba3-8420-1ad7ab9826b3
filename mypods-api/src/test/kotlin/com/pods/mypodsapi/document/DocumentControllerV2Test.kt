package com.pods.mypodsapi.document

import com.pods.mypodsapi.testUtils.myPodsMockMvcBuilder
import com.pods.mypodsapi.testUtils.myPodsObjectMapper
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import strikt.api.expectThat
import strikt.assertions.isEqualTo

class DocumentControllerV2Test{
 private val mockDocumentApiService = mockk<DocumentApiService>(relaxUnitFun = true)
 private val documentController = DocumentControllerV2(mockDocumentApiService)
 private val objectMapper = myPodsObjectMapper()

 private lateinit var mockMvc: MockMvc

 @BeforeEach
 fun setUp() {
  mockMvc = myPodsMockMvcBuilder(documentController).build()
 }
 @Nested
  inner class GetOrderDocuments {
    @Test
    fun `should return OK status and a list of order document-related data `() {
     val response = createOrderDocumentResponse()
     val request =
      MockMvcRequestBuilders
       .get("/v2/document/order-documents")
       .contentType(MediaType.APPLICATION_JSON)
     coEvery { mockDocumentApiService.getOrderDocuments(any(), any()) } returns response

     val result = mockMvc.perform(request).andReturn()

     expectThat(result.response.status).isEqualTo(HttpStatus.OK.value())
     expectThat(result.response.contentAsString).isEqualTo(objectMapper.writeValueAsString(response))

     verify { mockDocumentApiService.getOrderDocuments(any(), any()) }
    }
  }
 }
