package com.pods.mypodsapi.external.featureFlags

import com.pods.mypodsapi.external.featureFlags.FeatureFlagService.Companion.FEATURE_DISABLED
import com.pods.mypodsapi.external.featureFlags.FeatureFlagService.Companion.MAINTENANCE_MODE
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.split.client.SplitClient
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isFalse
import strikt.assertions.isTrue

class FeatureFlagServiceTest {
    private val splitClient = mockk<SplitClient>()
    private val featureFlagService = FeatureFlagService(splitClient = splitClient, poetEnabled = "true")

    @Nested
    inner class MaintenanceModeEnabled {
        @Test
        fun `should return false when getTreatment returns feature disabled`() {
            every { splitClient.getTreatment(any(), any()) } returns FEATURE_DISABLED

            val actual = featureFlagService.maintenanceModeEnabled(visitorId = VISITOR_ID)

            expectThat(actual).isFalse()
            verify { splitClient.getTreatment(VISITOR_ID, MAINTENANCE_MODE) }
        }

        @Test
        fun `should return true when getFeatureFlags returns alert`() {
            every { splitClient.getTreatment(any(), any()) } returns "alert"

            val actual = featureFlagService.maintenanceModeEnabled(visitorId = VISITOR_ID)

            expectThat(actual).isTrue()
        }

        @Test
        fun `should return true when getFeatureFlags returns form`() {
            every { splitClient.getTreatment(any(), any()) } returns "form"

            val actual = featureFlagService.maintenanceModeEnabled(visitorId = VISITOR_ID)

            expectThat(actual).isTrue()
        }
    }

    companion object {
        const val VISITOR_ID = "visitorId"
    }
}
