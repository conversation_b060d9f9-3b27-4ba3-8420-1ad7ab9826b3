package com.pods.mypodsapi.external.adapter

import com.pods.mypodsapi.PodsException
import com.pods.mypodsapi.external.featureFlags.FeatureFlagService
import com.pods.mypodsapi.external.locum.LocumClient
import com.pods.mypodsapi.external.locum.createLegacyLocumOrderData
import com.pods.mypodsapi.external.locum.createLocumCallInfo
import com.pods.mypodsapi.external.locum.createLocumContainer
import com.pods.mypodsapi.external.locum.createLocumDocumentsResponse
import com.pods.mypodsapi.external.locum.createLocumMoveLeg
import com.pods.mypodsapi.external.locum.createLocumOrder
import com.pods.mypodsapi.external.locum.createLocumOrdersResponse
import com.pods.mypodsapi.external.locum.createLocumSameServiceAreaResponse
import com.pods.mypodsapi.external.locum.createLocumUpdateMoveLegResponse
import com.pods.mypodsapi.external.locum.createUnsuccessfulLocumCallInfo
import com.pods.mypodsapi.external.locum.entities.LocumContainerPlacement
import com.pods.mypodsapi.external.locum.entities.LocumLegacyOrderDataResponse
import com.pods.mypodsapi.external.locum.entities.LocumUpdateMoveLegRequest
import com.pods.mypodsapi.external.locum.mappers.toLegacyOrderData
import com.pods.mypodsapi.external.locum.mappers.toLocumRequest
import com.pods.mypodsapi.external.locum.mappers.toOrder
import com.pods.mypodsapi.external.locum.mappers.toUpdateMoveLegResponse
import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import com.pods.mypodsapi.external.poetFacade.createPoetContainer
import com.pods.mypodsapi.external.poetFacade.createPoetDocument
import com.pods.mypodsapi.external.poetFacade.createPoetMoveLeg
import com.pods.mypodsapi.external.poetFacade.createPoetOrder
import com.pods.mypodsapi.external.poetFacade.createPoetServiceabilityResponse
import com.pods.mypodsapi.external.poetFacade.entities.ApplyOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.ApplyQuoteToOrderResponse
import com.pods.mypodsapi.external.poetFacade.entities.ContainerStatus
import com.pods.mypodsapi.external.poetFacade.entities.GenerateOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.GenerateQuoteForOrderChangesResponse
import com.pods.mypodsapi.external.poetFacade.entities.OrderStatus
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrderType
import com.pods.mypodsapi.external.poetFacade.entities.PoetServicingLocation
import com.pods.mypodsapi.external.poetFacade.entities.Timing
import com.pods.mypodsapi.external.poetFacade.entities.WarehouseId
import com.pods.mypodsapi.external.poetFacade.mappers.toPoetRequest
import com.pods.mypodsapi.external.poetFacade.toGenerateOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.toPoetLocationAvailabilityFields
import com.pods.mypodsapi.external.poetFacade.toPoetMoveLegAddress
import com.pods.mypodsapi.orders.ContainerPlacement
import com.pods.mypodsapi.orders.MoveLegType
import com.pods.mypodsapi.orders.PlacementType
import com.pods.mypodsapi.orders.SiteType
import com.pods.mypodsapi.orders.WarehouseHoursService
import com.pods.mypodsapi.orders.createSameServiceAreaRequest
import com.pods.mypodsapi.orders.createUpdateMoveLegRequest
import com.pods.mypodsapi.testUtils.jsonAssertEquals
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono
import strikt.api.expectThat
import strikt.api.expectThrows
import strikt.assertions.contains
import strikt.assertions.isEmpty
import strikt.assertions.isEqualTo
import strikt.assertions.isFalse
import strikt.assertions.isNotNull
import strikt.assertions.isNull
import strikt.assertions.isTrue
import java.time.LocalDate

class OrderAdapterTest {
    private val mockLocumClient = mockk<LocumClient>()
    private val mockFeatureFlagService = mockk<FeatureFlagService>()
    private val mockPoetFacadeClient = mockk<PoetFacadeClient>()
    private val warehouseHoursService = WarehouseHoursService(mockPoetFacadeClient)
    private val orderAdapter = OrderAdapter(mockLocumClient, mockFeatureFlagService, mockPoetFacadeClient, warehouseHoursService)
    private val customerId = "111222333"

    @Nested
    inner class GetLegacyOrderData {
        @Test
        fun `should return response from downstream service`() =
            runTest {
                val response = LocumLegacyOrderDataResponse(createLocumCallInfo(), listOf(createLegacyLocumOrderData()))
                every { mockLocumClient.getCustomerDocuments(any()) } returns Mono.just(createLocumDocumentsResponse())
                every { mockLocumClient.legacyAuthData(any(), any()) } returns Mono.just(response)

                val result = orderAdapter.getLegacyOrderData(customerId)

                expectThat(result).isEqualTo(response.response!!.toLegacyOrderData())
            }
    }

    @Nested
    inner class GetOrdersByCustomerId {
        @BeforeEach
        fun setup() {
            every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns false
        }

        @Test
        fun `returns orders from locum response`() =
            runTest {
                val ordersResponse = createLocumOrdersResponse()
                every { mockLocumClient.getOrdersByCustomerId(any()) } returns Mono.just(ordersResponse)

                val result = orderAdapter.getOrdersByCustomerId(customerId)

                expectThat(result).isEqualTo(ordersResponse.response!!.map { it.toOrder() })
                verify { mockLocumClient.getOrdersByCustomerId(customerId) }
            }

        @Test
        fun `converts stg legs to acs `() =
            runTest {
                val order =
                    createLocumOrder(
                        containers =
                            listOf(
                                createLocumContainer(moveLegs = listOf(createLocumMoveLeg(moveLegType = "STG"))),
                            ),
                    )
                val ordersResponse = createLocumOrdersResponse(response = listOf(order))
                every { mockLocumClient.getOrdersByCustomerId(any()) } returns Mono.just(ordersResponse)

                val result = orderAdapter.getOrdersByCustomerId(customerId)

                expectThat(result[0].containers[0].moveLegs[0].moveLegType).isEqualTo(MoveLegType.VISIT_CONTAINER)
            }

        @Nested
        inner class PoetFacade {
            private val activeContainer = createPoetContainer(status = ContainerStatus.BOOKED)
            private val bookedOrder =
                createPoetOrder(
                    orderId = "booked",
                    status = OrderStatus.BOOKED,
                    containers = listOf(activeContainer),
                )
            private val warehouseId1 = "test-warehouseId-1"
            private val warehouseId2 = "test-warehouseId-2"
            private val expectedStartTime = "10:00AM"
            private val expectedEndTime = "5:00PM"
            private val warehouseTimingsResponse =
                listOf(
                    mapOf(
                        this.warehouseId1 to
                            mapOf(
                                java.time.DayOfWeek.SUNDAY to Timing(startTime = "Closed", endTime = "Closed"),
                                java.time.DayOfWeek.MONDAY to Timing(startTime = "8:00AM", endTime = "3:00PM"),
                                java.time.DayOfWeek.FRIDAY to
                                    Timing(
                                        startTime = expectedStartTime,
                                        endTime = expectedEndTime,
                                    ),
                            ),
                    ),
                )

            @BeforeEach
            fun setUp() {
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns true

                every { mockPoetFacadeClient.getWarehousesTimings() } returns Mono.just(warehouseTimingsResponse)
            }

            @Test
            fun `returns orders from poet response`() =
                runTest {
                    val orders = listOf(createPoetOrder())
                    every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                    val result = orderAdapter.getOrdersByCustomerId(customerId)

                    expectThat(result).isEqualTo(orders.map { it.toOrder() })
                    verify { mockPoetFacadeClient.getOrdersByCustomerId(customerId) }
                    verify(exactly = 0) { mockLocumClient.getOrdersByCustomerId(any()) }
                }

            @Test
            fun `filters out orders with all cancelled containers`() {
                runTest {
                    val orderWithCancelledContainer =
                        createPoetOrder(
                            orderId = "orderWithCancelledContainer",
                            status = OrderStatus.BOOKED,
                            containers = listOf(createPoetContainer(status = ContainerStatus.CANCELLED)),
                        )
                    val orders = listOf(bookedOrder, orderWithCancelledContainer)
                    every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                    val result = orderAdapter.getOrdersByCustomerId(customerId)

                    expectThat(result.size).isEqualTo(1)
                    val firstOrder = result.first()
                    expectThat(firstOrder.orderId).isEqualTo(bookedOrder.orderId)
                    expectThat(firstOrder.containers.size).isEqualTo(1)
                    expectThat(firstOrder.containers.firstOrNull()?.containerId)
                        .isEqualTo(activeContainer.containerId)
                }
            }

            @Test
            fun `filters out containers with all cancelled moveLegs`() {
                runTest {
                    val orderWithCancelledMoveLegs =
                        createPoetOrder(
                            orderId = "orderWithCancelledMoveLegs",
                            status = OrderStatus.BOOKED,
                            containers =
                                listOf(
                                    createPoetContainer(
                                        status = ContainerStatus.BOOKED,
                                        moveLegs = listOf(createPoetMoveLeg(status = OrderStatus.CANCELLED)),
                                    ),
                                ),
                        )
                    val orders = listOf(bookedOrder, orderWithCancelledMoveLegs)
                    every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                    val result = orderAdapter.getOrdersByCustomerId(customerId)

                    expectThat(result.size).isEqualTo(1)
                    expectThat(result.first().orderId).isEqualTo(bookedOrder.orderId)
                    expectThat(result.find { it.orderId == orderWithCancelledMoveLegs.orderId }).isNull()
                }
            }

            @Test
            fun `filters out cancelled orders`() {
                runTest {
                    val orders =
                        listOf(
                            bookedOrder,
                            createPoetOrder(orderId = "cancelled", status = OrderStatus.CANCELLED),
                        )
                    every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                    val result = orderAdapter.getOrdersByCustomerId(customerId)

                    expectThat(result.size).isEqualTo(1)
                    expectThat(result.first().orderId).isEqualTo(bookedOrder.orderId)
                }
            }

            @Test
            fun `filters out orders with no active containers`() {
                runTest {
                    val orderWithNoContainers =
                        createPoetOrder(
                            orderId = "orderWithNoContainers",
                            status = OrderStatus.BOOKED,
                            containers = emptyList(),
                        )
                    val orders =
                        listOf(
                            bookedOrder,
                            orderWithNoContainers,
                        )
                    every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                    val result = orderAdapter.getOrdersByCustomerId(customerId)

                    expectThat(result.size).isEqualTo(1)
                    expectThat(result.first().orderId).isEqualTo(bookedOrder.orderId)
                }
            }

            @Test
            fun `gets warehouse timing for a scheduled ACS move leg`() =
                runTest {
                    val scheduledAcsMoveleg =
                        createPoetMoveLeg(
                            moveLegType = MoveLegType.VISIT_CONTAINER,
                            scheduledDate = LocalDate.parse("2024-02-02"),
                            siteIdentity = this@PoetFacade.warehouseId1,
                        )
                    val containers =
                        listOf(
                            createPoetContainer(
                                status = ContainerStatus.BOOKED,
                                moveLegs = listOf(scheduledAcsMoveleg),
                            ),
                        )
                    val orders =
                        listOf(createPoetOrder(containers = containers))
                    every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                    val result = orderAdapter.getOrdersByCustomerId(customerId)

                    expectThat(
                        result
                            .first()
                            .containers
                            .first()
                            .moveLegs
                            .first()
                            .eta,
                    ).isEqualTo("$expectedStartTime - $expectedEndTime")
                }

            @Test
            fun `if we are unable to get warehouse timings, do not change existing movelegs`() =
                runTest {
                    every { mockPoetFacadeClient.getWarehousesTimings() } throws (PodsException.notFound())
                    val scheduledAcsMoveleg =
                        createPoetMoveLeg(
                            moveLegType = MoveLegType.VISIT_CONTAINER,
                            scheduledDate = LocalDate.parse("2024-02-02"),
                            siteIdentity = this@PoetFacade.warehouseId1,
                        )
                    val containers =
                        listOf(
                            createPoetContainer(
                                status = ContainerStatus.BOOKED,
                                moveLegs = listOf(scheduledAcsMoveleg),
                            ),
                        )
                    val orders =
                        listOf(createPoetOrder(containers = containers))
                    every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                    val result = orderAdapter.getOrdersByCustomerId(customerId)

                    expectThat(
                        result,
                    ).isEqualTo(orders.map { it.toOrder() })
                }

            @Test
            fun `gets warehouse timing for a scheduled SELF_PICKUP move leg`() =
                runTest {
                    val scheduledAcsMoveleg =
                        createPoetMoveLeg(
                            moveLegType = MoveLegType.SELF_FINAL_PICKUP,
                            scheduledDate = LocalDate.parse("2024-02-02"),
                            siteIdentity = this@PoetFacade.warehouseId2,
                        )
                    val containers =
                        listOf(
                            createPoetContainer(
                                status = ContainerStatus.BOOKED,
                                moveLegs = listOf(scheduledAcsMoveleg),
                            ),
                        )
                    val orders =
                        listOf(createPoetOrder(containers = containers))
                    every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                    val result = orderAdapter.getOrdersByCustomerId(customerId)

                    expectThat(
                        result
                            .first()
                            .containers
                            .first()
                            .moveLegs
                            .first()
                            .eta,
                    ).isNull()
                }
        }
    }

    @Nested
    inner class GetOrderDocumentsForCustomer {
        private val incompletePoetDocument = createPoetDocument(tags = "Sent")
        private val completePoetDocument = createPoetDocument(tags = "Completed")

        private val poetDocuments = listOf(incompletePoetDocument, completePoetDocument)

        @Test
        fun `given poet documents, converts them to domain objects`() =
            runTest {
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns true
                every { mockPoetFacadeClient.getOrderDocuments(any()) } returns Mono.just(poetDocuments)

                val documents = orderAdapter.getOrderDocumentsForCustomer("cuid")

                expectThat(documents).isEqualTo(
                    listOf(
                        incompletePoetDocument.toDocument(),
                        completePoetDocument.toDocument(),
                    ),
                )
            }
    }

    @Nested
    inner class UpdateMoveLeg {
        @Nested
        inner class Locum {
            val request = createUpdateMoveLegRequest(orderId = "1234")

            @BeforeEach
            fun setup() {
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns false
            }

            @Test
            fun `returns update move leg response`() =
                runTest {
                    val locumResponse = createLocumUpdateMoveLegResponse()
                    every { mockLocumClient.updateMoveLeg(any()) } returns Mono.just(locumResponse)

                    val result = orderAdapter.updateMoveLeg(customerId, request)

                    expectThat(result).isEqualTo(locumResponse.toUpdateMoveLegResponse())
                    verify { mockLocumClient.updateMoveLeg(request.toLocumRequest(customerId)) }
                }

            @Test
            fun `properly maps container placement details to empty list`() =
                runTest {
                    val locumResponse = createLocumUpdateMoveLegResponse()
                    every { mockLocumClient.updateMoveLeg(any()) } returns Mono.just(locumResponse)
                    val requestSlot = slot<LocumUpdateMoveLegRequest>()

                    orderAdapter.updateMoveLeg(customerId, request)

                    verify { mockLocumClient.updateMoveLeg(capture(requestSlot)) }

                    expectThat(requestSlot.captured.containerPlacement).isNotNull().isEmpty()
                }

            @Test
            fun `properly maps container placement details with all details`() =
                runTest {
                    val locumResponse = createLocumUpdateMoveLegResponse()
                    every { mockLocumClient.updateMoveLeg(any()) } returns Mono.just(locumResponse)
                    val requestSlot = slot<LocumUpdateMoveLegRequest>()

                    orderAdapter.updateMoveLeg(
                        customerId,
                        request.copy(
                            containerPlacement =
                                ContainerPlacement(
                                    true,
                                    SiteType.PARKING_LOT,
                                    "These are driver notes!",
                                    PlacementType.DRIVEWAY_CIRCULAR_CLOSE_REAR,
                                ),
                        ),
                    )

                    verify { mockLocumClient.updateMoveLeg(capture(requestSlot)) }

                    val expectedContainerPlacement =
                        listOf(
                            LocumContainerPlacement("IsPavedSurface", "true"),
                            LocumContainerPlacement("SiteType", "parkinglot"),
                            LocumContainerPlacement("ContainerPlacement", "1d_doors_03_b"),
                            LocumContainerPlacement("PlacementNotes", "These are driver notes!"),
                        )

                    jsonAssertEquals(expectedContainerPlacement, requestSlot.captured.containerPlacement!!)
                }

            @Test
            fun `properly maps container placement with default placement details when none provided`() =
                runTest {
                    val locumResponse = createLocumUpdateMoveLegResponse()
                    every { mockLocumClient.updateMoveLeg(any()) } returns Mono.just(locumResponse)
                    val requestSlot = slot<LocumUpdateMoveLegRequest>()

                    orderAdapter.updateMoveLeg(
                        customerId,
                        request.copy(
                            containerPlacement =
                                ContainerPlacement(
                                    true,
                                ),
                        ),
                    )

                    verify { mockLocumClient.updateMoveLeg(capture(requestSlot)) }

                    val expectedContainerPlacement =
                        listOf(
                            LocumContainerPlacement("IsPavedSurface", "true"),
                            LocumContainerPlacement("SiteType", "driveway"),
                            LocumContainerPlacement("ContainerPlacement", "1b_doors_01_a"),
                            LocumContainerPlacement("PlacementNotes", ""),
                        )

                    jsonAssertEquals(expectedContainerPlacement, requestSlot.captured.containerPlacement!!)
                }

            @Test
            fun `throws exception when locum call info is unsuccessful`() {
                val locumResponse = createLocumUpdateMoveLegResponse(callInfo = createUnsuccessfulLocumCallInfo())
                every { mockLocumClient.updateMoveLeg(any()) } returns Mono.just(locumResponse)

                expectThrows<Exception> {
                    orderAdapter.updateMoveLeg(customerId, request)
                }
            }

            @Test
            fun `throws 400 exception when locum call returns a 400 with move leg Id not found `() {
                every { mockLocumClient.updateMoveLeg(any()) } throws
                    WebClientResponseException.create(
                        400,
                        "Move Leg Id not found :  131434262P1S2",
                        org.springframework.http.HttpHeaders(),
                        ByteArray(0),
                        null,
                    )

                val exception =
                    expectThrows<PodsException> {
                        orderAdapter.updateMoveLeg(customerId, request)
                    }

                expectThat(exception.subject.httpStatus).isEqualTo(HttpStatus.BAD_REQUEST)
                expectThat(exception.subject.internalMessage).contains("Move Leg Id not found :  131434262P1S2")
                expectThat(exception.subject.externalMessage).isEqualTo(STALE_DATA_ERROR_MESSAGE)
            }
        }

        @Nested
        inner class POET {
            private val request =
                createUpdateMoveLegRequest(
                    containerOrderId = "testContainerOrderId",
                    requestedDate = LocalDate.now().plusDays(10),
                )
            private val sfQuoteId = "12345"
            private val zeroPriceDifference = 0L
            private val generateQuoteWithZeroPriceDiff =
                GenerateQuoteForOrderChangesResponse(
                    sfQuoteId = sfQuoteId,
                    priceDifference = zeroPriceDifference,
                )
            private val nonZeroPriceDifference = 111L
            private val generateQuoteWithPriceDiff =
                GenerateQuoteForOrderChangesResponse(
                    sfQuoteId = sfQuoteId,
                    priceDifference = nonZeroPriceDifference,
                )

            @BeforeEach
            fun setup() {
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns true
            }

            @Test
            fun `should generate quote and apply it to order when priceDiff is 0L`() =
                runTest {
                    val applyQuoteResponse = ApplyQuoteToOrderResponse(sfQuoteId = sfQuoteId)
                    every { mockPoetFacadeClient.generateOrderChanges(any()) } returns Mono.just(generateQuoteWithZeroPriceDiff)
                    every { mockPoetFacadeClient.applyQuoteToOrder(any()) } returns Mono.just(applyQuoteResponse)

                    val updateMoveLegResponse = orderAdapter.updateMoveLeg(customerId, request)

                    verify { mockPoetFacadeClient.generateOrderChanges(request.toGenerateOrderChangesRequest()) }
                    verify { mockPoetFacadeClient.applyQuoteToOrder(ApplyOrderChangesRequest(sfQuoteId)) }
                    expectThat(updateMoveLegResponse.quoteId).isNull()
                    expectThat(updateMoveLegResponse.priceDifference).isNull()
                }

            @Test
            fun `should generate quote and SKIP applying it to the order when priceDiff is NOT 0L`() =
                runTest {
                    val expectedPriceDiffInPercentage = String.format("%.2f", nonZeroPriceDifference / 100.0)
                    every { mockPoetFacadeClient.generateOrderChanges(any()) } returns Mono.just(generateQuoteWithPriceDiff)

                    val updateMoveLegResponse = orderAdapter.updateMoveLeg(customerId, request)

                    verify { mockPoetFacadeClient.generateOrderChanges(request.toGenerateOrderChangesRequest()) }
                    verify(exactly = 0) { mockPoetFacadeClient.applyQuoteToOrder(ApplyOrderChangesRequest(sfQuoteId)) }
                    expectThat(updateMoveLegResponse.priceDifference).isEqualTo(expectedPriceDiffInPercentage)
                    expectThat(updateMoveLegResponse.quoteId).isEqualTo(sfQuoteId)
                }

            @Test
            fun `should update origin address if movelegType is a type of pickup`() =
                runTest {
                    val pickUpRequest =
                        createUpdateMoveLegRequest(
                            containerOrderId = "testContainerOrderId",
                            requestedDate = LocalDate.now().plusDays(10),
                            moveLegType = MoveLegType.SELF_FINAL_PICKUP,
                        )

                    val generateOrderChangesRequest =
                        GenerateOrderChangesRequest(
                            orderId = pickUpRequest.orderId,
                            containerOrderId = pickUpRequest.containerOrderId!!,
                            requestedDate = pickUpRequest.requestedDate!!,
                            moveLegId = pickUpRequest.moveLegId,
                            requestedServiceAddress = pickUpRequest.serviceAddress?.toPoetMoveLegAddress(),
                            isRequestedServiceForOrigin = true,
                            containerPlacement = null,
                            locationAvailabilityFields = pickUpRequest.locationFields.toPoetLocationAvailabilityFields(pickUpRequest.requestedDate),
                        )
                    every { mockPoetFacadeClient.generateOrderChanges(any()) } returns Mono.just(generateQuoteWithPriceDiff)

                    orderAdapter.updateMoveLeg(customerId, pickUpRequest)

                    verify { mockPoetFacadeClient.generateOrderChanges(generateOrderChangesRequest) }
                }

            @Test
            fun `should update destination address if movelegType is NOT type of pickup`() =
                runTest {
                    val pickUpRequest =
                        createUpdateMoveLegRequest(
                            containerOrderId = "testContainerOrderId",
                            requestedDate = LocalDate.now().plusDays(10),
                            moveLegType = MoveLegType.REDELIVERY,
                        )

                    val generateOrderChangesRequest =
                        GenerateOrderChangesRequest(
                            orderId = pickUpRequest.orderId,
                            containerOrderId = pickUpRequest.containerOrderId!!,
                            requestedDate = pickUpRequest.requestedDate!!,
                            moveLegId = pickUpRequest.moveLegId,
                            requestedServiceAddress = pickUpRequest.serviceAddress?.toPoetMoveLegAddress(),
                            isRequestedServiceForOrigin = false,
                            locationAvailabilityFields = pickUpRequest.locationFields.toPoetLocationAvailabilityFields(pickUpRequest.requestedDate),
                            containerPlacement = null,
                        )
                    every { mockPoetFacadeClient.generateOrderChanges(any()) } returns Mono.just(generateQuoteWithPriceDiff)

                    orderAdapter.updateMoveLeg(customerId, pickUpRequest)

                    verify { mockPoetFacadeClient.generateOrderChanges(generateOrderChangesRequest) }
                }
        }
    }

    @Nested
    inner class IsSameServiceArea {
        val request = createSameServiceAreaRequest()

        @BeforeEach
        fun setUp() {
            every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns false
        }

        @Test
        fun `returns isSameServiceAddress response`() =
            runTest {
                val locumResponse = createLocumSameServiceAreaResponse()
                every { mockLocumClient.isSameServiceAddress(any()) } returns Mono.just(locumResponse)

                val result = orderAdapter.isSameServiceArea(request, customerId)

                expectThat(result).isEqualTo(locumResponse.isSameServiceArea)
                verify { mockLocumClient.isSameServiceAddress(request.toLocumRequest()) }
            }

        @Nested
        inner class PoetFacade {
            val request = createSameServiceAreaRequest()

            @BeforeEach
            fun setUp() {
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns true
            }

            @Test
            fun `returns true if the warehouse IDs match for origination and destination`() =
                runTest {
                    val poetResponse =
                        createPoetServiceabilityResponse(
                            servicingLocations =
                                listOf(
                                    PoetServicingLocation(
                                        isSelected = true,
                                        orderType = PoetOrderType.LOCAL,
                                        origination = WarehouseId("test-warehouse-id"),
                                        destination = WarehouseId("test-warehouse-id"),
                                    ),
                                ),
                        )
                    every { mockPoetFacadeClient.getServiceability(any()) } returns Mono.just(poetResponse)

                    val result = orderAdapter.isSameServiceArea(request, customerId)

                    expectThat(result).isTrue()
                    verify { mockPoetFacadeClient.getServiceability(request.toPoetRequest()) }
                }

            @Test
            fun `returns true if the warehouse IDs DO NOT match for origination and destination`() =
                runTest {
                    val poetResponse =
                        createPoetServiceabilityResponse(
                            servicingLocations =
                                listOf(
                                    PoetServicingLocation(
                                        isSelected = true,
                                        orderType = PoetOrderType.LOCAL,
                                        origination = WarehouseId("test-warehouse-id"),
                                        destination = WarehouseId("other-warehouse-id"),
                                    ),
                                ),
                        )
                    every { mockPoetFacadeClient.getServiceability(any()) } returns Mono.just(poetResponse)

                    val result = orderAdapter.isSameServiceArea(request, customerId)

                    expectThat(result).isFalse()
                    verify { mockPoetFacadeClient.getServiceability(request.toPoetRequest()) }
                }
        }
    }
}
