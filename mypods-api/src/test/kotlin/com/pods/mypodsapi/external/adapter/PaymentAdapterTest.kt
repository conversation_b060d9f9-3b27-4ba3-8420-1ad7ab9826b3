package com.pods.mypodsapi.external.adapter

import com.pods.mypodsapi.PodsException
import com.pods.mypodsapi.customers.exceptions.PayInvoiceCreditCardIssue
import com.pods.mypodsapi.external.featureFlags.FeatureFlagService
import com.pods.mypodsapi.external.locum.LocumClient
import com.pods.mypodsapi.external.locum.createLocumCallInfo
import com.pods.mypodsapi.external.locum.createLocumPayInvoiceRequest
import com.pods.mypodsapi.external.locum.createLocumPaymentMethod
import com.pods.mypodsapi.external.locum.createLocumPaymentMethodsResponse
import com.pods.mypodsapi.external.locum.createUnsuccessfulLocumCallInfo
import com.pods.mypodsapi.external.locum.entities.LocumAddPaymentMethodRequest
import com.pods.mypodsapi.external.locum.entities.LocumAddPaymentMethodResponse
import com.pods.mypodsapi.external.locum.entities.LocumPayInvoiceResponse
import com.pods.mypodsapi.external.locum.entities.LocumUpdatePaymentMethodResponse
import com.pods.mypodsapi.external.locum.mappers.toLocumRequest
import com.pods.mypodsapi.external.locum.mappers.toPaymentMethods
import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import com.pods.mypodsapi.external.poetFacade.createCreditCard
import com.pods.mypodsapi.external.poetFacade.createCreditLine
import com.pods.mypodsapi.external.poetFacade.entities.PoetInvoicePaymentErrorEnum
import com.pods.mypodsapi.external.poetFacade.entities.PoetPaymentMethodResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetPaymentMethodType
import com.pods.mypodsapi.external.poetFacade.entities.PoetSetDefaultPaymentRequest
import com.pods.mypodsapi.external.poetFacade.mappers.toPaymentMethods
import com.pods.mypodsapi.external.poetFacade.mappers.toPoetRequest
import com.pods.mypodsapi.payments.PaymentMethodType
import com.pods.mypodsapi.payments.SetDefaultPaymentMethodRequest
import com.pods.mypodsapi.payments.createInvoiceToPay
import com.pods.mypodsapi.payments.createMakePaymentRequest
import com.pods.mypodsapi.payments.createPoetInvoicePaymentError
import com.pods.mypodsapi.payments.createPoetInvoicePaymentResponse
import com.pods.mypodsapi.payments.createSetDefaultPaymentMethodRequest
import com.pods.mypodsapi.payments.createVerifiedAddPaymentMethodRequest
import com.pods.mypodsapi.testUtils.myPodsObjectMapper
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.ArgumentsProvider
import org.junit.jupiter.params.provider.ArgumentsSource
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono
import strikt.api.expectThat
import strikt.api.expectThrows
import strikt.assertions.hasSize
import strikt.assertions.isA
import strikt.assertions.isEmpty
import strikt.assertions.isEqualTo
import strikt.assertions.isTrue
import java.nio.charset.StandardCharsets
import java.util.stream.Stream

class PaymentAdapterTest {
    private val mockLocumClient = mockk<LocumClient>()
    private val objectMapper = myPodsObjectMapper()
    private val mockFeatureFlagService = mockk<FeatureFlagService>()
    private val mockPoetFacadeClient = mockk<PoetFacadeClient>()
    private val paymentAdapter =
        PaymentAdapter(mockLocumClient, mockk(), objectMapper, mockFeatureFlagService, mockPoetFacadeClient)

    @Nested
    inner class GetPaymentMethods {
        private val customerId = "111222333"

        @Nested
        inner class Locum {
            private val paymentsResponse = createLocumPaymentMethodsResponse()

            @BeforeEach
            fun setup() {
                every { mockLocumClient.getPaymentMethodsByCustomerId(any()) } returns Mono.just(paymentsResponse)
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns false
            }

            @Test
            fun `should return payment info from locum by customerId`() =
                runTest {
                    val expected = paymentsResponse.toPaymentMethods()

                    val actual = paymentAdapter.getPaymentMethods(customerId)
                    expectThat(actual).isEqualTo(expected)

                    verify { mockLocumClient.getPaymentMethodsByCustomerId(customerId) }
                }

            @Test
            fun `should return accountId when method is PayPal`() =
                runTest {
                    val expectedAccountId = "<EMAIL>"
                    val locumResponse =
                        listOf(createLocumPaymentMethod(cardType = "PayPal", cardNumber = expectedAccountId))
                    val paymentsResponse = createLocumPaymentMethodsResponse(response = locumResponse)
                    every { mockLocumClient.getPaymentMethodsByCustomerId(any()) } returns Mono.just(paymentsResponse)

                    val actual = paymentAdapter.getPaymentMethods(customerId)
                    expectThat(actual.firstOrNull()?.accountId).isEqualTo(expectedAccountId)
                }
        }

        @Nested
        inner class Poet {
            private val poetPaymentsResponse =
                PoetPaymentMethodResponse(
                    creditCards = listOf(createCreditCard()),
                    creditLines = listOf(createCreditLine()),
                )

            @BeforeEach
            fun setup() {
                every { mockPoetFacadeClient.getPaymentMethods(any()) } returns Mono.just(poetPaymentsResponse)
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns true
            }

            @Test
            fun `should return payment info from poet by customerId`() =
                runTest {
                    val expected = poetPaymentsResponse.toPaymentMethods()

                    val actual = paymentAdapter.getPaymentMethods(customerId)
                    expectThat(actual).isEqualTo(expected)

                    verify { mockPoetFacadeClient.getPaymentMethods(customerId) }
                }

            @Test
            fun `should return an empty list when PoetFacade returns a 404 exception`() =
                runTest {
                    val errorMessage = "Customer not found"

                    every { mockPoetFacadeClient.getPaymentMethods(any()) } throws
                        WebClientResponseException.create(
                            404,
                            "Not found",
                            org.springframework.http.HttpHeaders(),
                            errorMessage.toByteArray(StandardCharsets.UTF_8),
                            null,
                        )

                    val actualResult = paymentAdapter.getPaymentMethods(customerId)

                    expectThat(actualResult).isEmpty()
                }

            @Test
            fun `should return a 500 Internal Error when PoetFacade returns any non 404 exception`() =
                runTest {
                    val errorMessage = "Some internal error message"
                    val externalErrorMessage = "Unable to find payment methods. Try again later."

                    every { mockPoetFacadeClient.getPaymentMethods(any()) } throws
                        WebClientResponseException.create(
                            400,
                            "",
                            org.springframework.http.HttpHeaders(),
                            errorMessage.toByteArray(StandardCharsets.UTF_8),
                            null,
                        )

                    val exception =
                        expectThrows<PodsException> {
                            paymentAdapter.getPaymentMethods(customerId)
                        }.subject

                    expectThat(exception.httpStatus).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR)
                    expectThat(exception.internalMessage).isEqualTo(errorMessage)
                    expectThat(exception.externalMessage).isEqualTo(externalErrorMessage)
                }
        }
    }

    @Nested
    inner class SetDefaultPaymentMethod {
        private val customerId = "111222333"
        private val request = createSetDefaultPaymentMethodRequest()
        private val successfulResponse = LocumUpdatePaymentMethodResponse(createLocumCallInfo())
        private val failedResponse = LocumUpdatePaymentMethodResponse(createUnsuccessfulLocumCallInfo())

        @Nested
        inner class Locum {
            @BeforeEach
            fun setup() {
                every { mockLocumClient.updatePaymentMethod(any(), any()) } returns Mono.just(successfulResponse)
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns false
            }

            @Test
            fun `should update payment method`() =
                runTest {
                    paymentAdapter.setDefaultPaymentMethod(customerId, request)

                    val locumRequest = request.toLocumRequest()
                    expectThat(locumRequest.isPrimary).isTrue()
                    verify { mockLocumClient.updatePaymentMethod(customerId, locumRequest) }
                }

            @Test
            fun `should throw exception if response call info is not ok`() {
                every { mockLocumClient.updatePaymentMethod(any(), any()) } returns Mono.just(failedResponse)

                expectThrows<PodsException> {
                    paymentAdapter.setDefaultPaymentMethod(customerId, request)
                }
            }
        }

        @Nested
        inner class Poet {
            @BeforeEach
            fun setup() {
                every { mockPoetFacadeClient.setDefaultPaymentMethod(any()) } returns Mono.just(Unit)
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns true
            }

            @Test
            fun `should set default payment method`() =
                runTest {
                    val paymentMethodId = "1212"
                    val paymentMethodType = PaymentMethodType.CREDIT_CARD

                    val request = SetDefaultPaymentMethodRequest(paymentMethodId, paymentMethodType)
                    val expectedRequest =
                        PoetSetDefaultPaymentRequest(
                            customerId = customerId,
                            paymentMethodType = PoetPaymentMethodType.valueOf(paymentMethodType.name),
                            paymentMethodId = request.paymentMethodId,
                            isPrimary = true,
                        )

                    paymentAdapter.setDefaultPaymentMethod(customerId, request)

                    verify { mockPoetFacadeClient.setDefaultPaymentMethod(expectedRequest) }
                }
        }
    }

    @Nested
    inner class AddPaymentMethod {
        private val customerId = "111222333"
        private val request = createVerifiedAddPaymentMethodRequest()
        private val successfulResponse = LocumAddPaymentMethodResponse(createLocumCallInfo())
        private val failedResponse = LocumAddPaymentMethodResponse(createUnsuccessfulLocumCallInfo())

        @BeforeEach
        fun setup() {
            every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns false
        }

        @Nested
        inner class Locum {
            @BeforeEach
            fun setup() {
                every { mockLocumClient.addPaymentMethod(any(), any()) } returns Mono.just(successfulResponse)
            }

            @Test
            fun `should add new payment method`() =
                runTest {
                    paymentAdapter.addPaymentMethod(customerId, request)

                    val locumRequest = request.toLocumRequest(customerId)
                    verify { mockLocumClient.addPaymentMethod(customerId, locumRequest) }
                }

            @Test
            fun `should throw exception if response call info is not ok`() {
                every { mockLocumClient.addPaymentMethod(any(), any()) } returns Mono.just(failedResponse)

                expectThrows<PodsException> {
                    paymentAdapter.addPaymentMethod(customerId, request)
                }
            }

            @Test
            fun `maps expiration date to locum format`() =
                runTest {
                    val request =
                        createVerifiedAddPaymentMethodRequest(cardExpirationMonth = "03", cardExpirationYear = "2030")

                    paymentAdapter.addPaymentMethod(customerId, request)

                    val slot = slot<LocumAddPaymentMethodRequest>()
                    verify { mockLocumClient.addPaymentMethod(any(), capture(slot)) }
                    expectThat(slot.captured.cardExpirationDate).isEqualTo("03/2030")
                }

            @Test
            fun `maps credit card last four to 0 if null`() =
                runTest {
                    val request = createVerifiedAddPaymentMethodRequest(cardNumberLastFourDigits = null)

                    paymentAdapter.addPaymentMethod(customerId, request)

                    val slot = slot<LocumAddPaymentMethodRequest>()
                    verify { mockLocumClient.addPaymentMethod(any(), capture(slot)) }
                    expectThat(slot.captured.cardNumber).isEqualTo(request.cardNumber)
                    expectThat(slot.captured.cardNumberLastFourDigits).isEqualTo("0")
                }

            @ParameterizedTest
            @ArgumentsSource(ArgumentsForCreditCardType::class)
            fun `maps credit card type to locum format`(
                givenCardType: String,
                expectedCardType: String,
            ) = runTest {
                val request = createVerifiedAddPaymentMethodRequest(cardType = givenCardType)

                paymentAdapter.addPaymentMethod(customerId, request)

                val slot = slot<LocumAddPaymentMethodRequest>()
                verify { mockLocumClient.addPaymentMethod(any(), capture(slot)) }
                expectThat(slot.captured.cardType).isEqualTo(expectedCardType)
            }

            @Test
            fun `should throw 400 BAD_REQUEST if response exception is about invalid credit card`() {
                val errorMessage = "A valid credit card expiration must be provided."
                val responseBody = errorMessage.toByteArray(StandardCharsets.UTF_8)

                every { mockLocumClient.addPaymentMethod(any(), any()) } throws
                    WebClientResponseException.create(
                        500,
                        "",
                        org.springframework.http.HttpHeaders(),
                        responseBody,
                        null,
                    )

                val exception =
                    expectThrows<PodsException> { paymentAdapter.addPaymentMethod(customerId, request) }.subject

                expectThat(exception.httpStatus).isEqualTo(HttpStatus.BAD_REQUEST)
                expectThat(exception.externalMessage).isEqualTo("Card Not Added: Expiration Date Must Be At Least 30 Days In The Future")
            }
        }

        @Nested
        inner class Poet {
            @BeforeEach
            fun setup() {
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns true
                every { mockPoetFacadeClient.addPaymentMethod(any()) } returns Mono.just(Unit)
            }

            @Test
            fun `should add payment method`() =
                runTest {
                    paymentAdapter.addPaymentMethod(customerId, request)

                    verify { mockPoetFacadeClient.addPaymentMethod(request.toPoetRequest(customerId)) }
                }
        }
    }

    @Nested
    inner class MakePayment {
        private val customerId = "111222333"
        private val invoiceToPay = createInvoiceToPay()
        private val request = createMakePaymentRequest()
        private val successfulResponse = LocumPayInvoiceResponse(createLocumCallInfo())
        private val failedResponse = LocumPayInvoiceResponse(createUnsuccessfulLocumCallInfo())

        @BeforeEach
        fun setup() {
            every { mockLocumClient.payInvoice(any()) } returns Mono.just(successfulResponse)
            every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns false
        }

        @Test
        fun `Calls LocumClient with proper request`() =
            runTest {
                paymentAdapter.makePayment(customerId, request)
                val locumRequest =
                    createLocumPayInvoiceRequest(
                        userId = customerId,
                        invoiceNumber = invoiceToPay.invoiceNumber,
                        paymentAmount = invoiceToPay.paymentAmount,
                        paymentMethodId = invoiceToPay.paymentMethodId,
                    )
                verify { mockLocumClient.payInvoice(locumRequest) }
            }

        @Test
        fun `throws exception if locumClient throws exception`() =
            runTest {
                every { mockLocumClient.payInvoice(any()) } returns Mono.just(failedResponse)
                val exceptions = paymentAdapter.makePayment(customerId, request)
                expectThat(exceptions).hasSize(1)
                expectThat(exceptions.first()).isA<PodsException>()
            }

        @Nested
        @TestInstance(TestInstance.Lifecycle.PER_CLASS)
        inner class Exception {
            @ParameterizedTest
            @MethodSource("creditCardExceptionArguments")
            fun `returns exception if locumClient call has a CCIssue`(
                locumMessage: String,
                errorStatus: PayInvoiceCreditCardIssue,
            ) = runTest {
                val ccIssueCallInfo =
                    createLocumCallInfo(
                        statusCode = "CCIssue",
                        statusMessage = locumMessage,
                    )
                val ccIssueLocumResponse = LocumPayInvoiceResponse(ccIssueCallInfo)
                every { mockLocumClient.payInvoice(any()) } returns Mono.just(ccIssueLocumResponse)

                val exceptions = paymentAdapter.makePayment(customerId, request)
                expectThat(exceptions).hasSize(1)
                val expectedException = exceptions.first() as PodsException
                expectThat(expectedException.httpStatus).isEqualTo(HttpStatus.BAD_REQUEST)
                expectThat(expectedException.errorStatus).isEqualTo(errorStatus.toString())
            }

            private fun creditCardExceptionArguments(): List<Arguments> =
                listOf(
                    Arguments.of(
                        "<Decline - null Processor Declined - Fraud Suspected>",
                        PayInvoiceCreditCardIssue.FRAUD_SUSPECTED,
                    ),
                    Arguments.of(
                        "Invoice 'PODS007980123' for customer '123456789' has been fully settled." +
                            " Unable to apply settlement to it.>",
                        PayInvoiceCreditCardIssue.NO_BALANCE_REMAINING,
                    ),
                    Arguments.of(
                        "Decline\r A credit card payment was attempted and failed." +
                            "The processor returned the following message: <Decline - null Closed Card>",
                        PayInvoiceCreditCardIssue.CARD_CLOSED,
                    ),
                    Arguments.of(
                        "Decline\r A credit card payment was attempted and failed." +
                            "The processor returned the following message: <Decline - null Insufficient Funds>",
                        PayInvoiceCreditCardIssue.INSUFFICIENT_FUNDS,
                    ),
                    Arguments.of(
                        "Decline\r A credit card payment was attempted and failed." +
                            " The processor returned the following message: <Decline - null Limit Exceeded>",
                        PayInvoiceCreditCardIssue.LIMIT_EXCEEDED,
                    ),
                    Arguments.of(
                        "Decline\r+ : A credit card payment was attempted and failed.  The processor returned the following message: <Decline - null Declined>",
                        PayInvoiceCreditCardIssue.DECLINED,
                    ),
                    Arguments.of(
                        "Decline - Fault was thrown by the service for request a9d7b9f8-48dc-4835-93de-e874842229e0. Exception details:\n" +
                            "      Type: Microsoft.Dynamics.Ax.Xpp.ErrorException\n" +
                            "      Message: Invoice 'PODS007582699' for customer '168455200' is already marked for settlement in D365",
                        PayInvoiceCreditCardIssue.NO_BALANCE_REMAINING,
                    ),
                    Arguments.of(
                        "Decline\r : A credit card payment was attempted and failed.  The processor returned the following message: <Decline - null Card Not Activated>",
                        PayInvoiceCreditCardIssue.INACTIVE_CARD,
                    ),
                    Arguments.of(
                        "Decline\r : A credit card payment was attempted and failed.  The processor returned the following message: <Decline - null Funding Instrument In The PayPal Account Was >",
                        PayInvoiceCreditCardIssue.PAYPAL_ACCOUNT_ISSUE,
                    ),
                    Arguments.of(
                        "Decline\r : A credit card payment was attempted and failed.  The processor returned the following message: <Decline - null Do Not Honor>",
                        PayInvoiceCreditCardIssue.BANK_PAYMENT_UNAUTHORISED,
                    ),
                    Arguments.of(
                        "Decline\r : A credit card payment was attempted and failed.  The processor returned the following message: <Decline - null Declined - Call Issuer>",
                        PayInvoiceCreditCardIssue.CARD_ISSUER_DECLINED,
                    ),
                    Arguments.of(
                        "Decline\r : A credit card payment was attempted and failed.  The processor returned the following message: <Decline - null No Account>",
                        PayInvoiceCreditCardIssue.INVALID_ACCOUNT,
                    ),
                    Arguments.of(
                        "Decline\r : A credit card payment was attempted and failed.  The processor returned the following message: <Decline - null Processor Declined>",
                        PayInvoiceCreditCardIssue.PROCESSOR_DECLINED,
                    ),
                )
        }

        @Nested
        inner class Poet {
            private val noPaymentFailures = createPoetInvoicePaymentResponse()

            @BeforeEach
            fun setup() {
                every { mockFeatureFlagService.poetFacadeEnabled(any()) } returns true
                every { mockPoetFacadeClient.makePayments(any()) } returns Mono.just(noPaymentFailures)
            }

            @Test
            fun `when poet enable flag is on call make payment`() =
                runTest {
                    val actual = paymentAdapter.makePayment(customerId, request)

                    expectThat(actual).isEmpty()
                    verify { mockPoetFacadeClient.makePayments(request.toPoetRequest(customerId)) }
                    verify { mockLocumClient wasNot Called }
                }

            @Test
            fun `when poet returns errors, return related error exceptions`() =
                runTest {
                    val errorReason1 = PoetInvoicePaymentErrorEnum.PROCESSOR_DECLINED
                    val errorReason2 = PoetInvoicePaymentErrorEnum.BANK_PAYMENT_UNAUTHORISED
                    val error1 = createPoetInvoicePaymentError(errorReason = errorReason1)
                    val error2 = createPoetInvoicePaymentError(errorReason = errorReason2)
                    val paymentFailures = createPoetInvoicePaymentResponse(listOf(error1, error2))
                    val expected =
                        listOf(
                            errorReason1.payInvoiceCreditCardException,
                            errorReason2.payInvoiceCreditCardException,
                        )

                    val byteResponseBody = objectMapper.writeValueAsString(paymentFailures).toByteArray(StandardCharsets.UTF_8)

                    val badRequestException =
                        WebClientResponseException.create(
                            400,
                            "Bad Request",
                            HttpHeaders(),
                            byteResponseBody,
                            StandardCharsets.UTF_8,
                        )

                    every { mockPoetFacadeClient.makePayments(any()) } throws badRequestException

                    val actual = paymentAdapter.makePayment(customerId, request)

                    expectThat(actual).isEqualTo(expected)
                    verify { mockPoetFacadeClient.makePayments(request.toPoetRequest(customerId)) }
                    verify { mockLocumClient wasNot Called }
                }
        }
    }

    companion object {
        class ArgumentsForCreditCardType : ArgumentsProvider {
            override fun provideArguments(context: ExtensionContext?): Stream<out Arguments> =
                Stream.of(
                    // Braintree responses
                    Arguments.of("Visa", "VISA"),
                    Arguments.of("Mastercard", "MASTERCARD"),
                    Arguments.of("American Express", "AMERICANEXPRESS"),
                    Arguments.of("Discover", "DISCOVER"),
                    Arguments.of("PAYPAL", "PAYPAL"),
                    // "Unknown" cards from https://developer.paypal.com/braintree/docs/guides/credit-cards/testing-go-live/java#valid-card-numbers
                    Arguments.of("JCB", ""),
                    Arguments.of("Maestro", ""),
                    Arguments.of("UnionPay", ""),
                )
        }
    }
}
