package com.pods.mypodsapi.external.poetFacade

import com.pods.mypodsapi.customers.CustomerType
import com.pods.mypodsapi.external.poetFacade.entities.ContainerStatus
import com.pods.mypodsapi.external.poetFacade.entities.Currency
import com.pods.mypodsapi.external.poetFacade.entities.GenerateOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.OrderStatus
import com.pods.mypodsapi.external.poetFacade.entities.PoetAccountAddress
import com.pods.mypodsapi.external.poetFacade.entities.PoetAccountPhone
import com.pods.mypodsapi.external.poetFacade.entities.PoetBillingAddressResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetCallInfo
import com.pods.mypodsapi.external.poetFacade.entities.PoetContainer
import com.pods.mypodsapi.external.poetFacade.entities.PoetContainerSize
import com.pods.mypodsapi.external.poetFacade.entities.PoetCreditCard
import com.pods.mypodsapi.external.poetFacade.entities.PoetCreditLine
import com.pods.mypodsapi.external.poetFacade.entities.PoetCustomer
import com.pods.mypodsapi.external.poetFacade.entities.PoetDocument
import com.pods.mypodsapi.external.poetFacade.entities.PoetDocumentType
import com.pods.mypodsapi.external.poetFacade.entities.PoetInvoice
import com.pods.mypodsapi.external.poetFacade.entities.PoetLocationAvailabilityFields
import com.pods.mypodsapi.external.poetFacade.entities.PoetMoveLeg
import com.pods.mypodsapi.external.poetFacade.entities.PoetMoveLegAddress
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrder
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrderType
import com.pods.mypodsapi.external.poetFacade.entities.PoetPostalCodeException
import com.pods.mypodsapi.external.poetFacade.entities.PoetServiceabilityResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetServicingLocation
import com.pods.mypodsapi.orders.ContainerPlacement
import com.pods.mypodsapi.orders.MoveLegType
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

fun createPoetCustomer(
    cuid: String = "*********",
    email: String = "<EMAIL>",
    firstName: String = "John",
    lastName: String = "Doe",
    smsOptIn: Boolean = true,
    customerType: CustomerType = CustomerType.RESIDENTIAL,
    billingAddress: PoetAccountAddress = createPoetAddress(),
    shippingAddress: PoetAccountAddress = createPoetAddress(),
    primaryPhone: PoetAccountPhone = PoetAccountPhone("*********3"),
    secondaryPhone: PoetAccountPhone = PoetAccountPhone("*********3"),
    militaryStatus: String = "unknown",
    militaryBranch: String = "unknown"
) = PoetCustomer(
    cuid = cuid,
    email = email,
    firstName = firstName,
    lastName = lastName,
    smsOptIn = smsOptIn,
    customerType = customerType,
    billingAddress = billingAddress,
    shippingAddress = shippingAddress,
    primaryPhone = primaryPhone,
    secondaryPhone = secondaryPhone,
    militaryStatus = militaryStatus,
    militaryBranch = militaryBranch
)

fun createPoetOrder(
    orderId: String = "1",
    orderDate: LocalDateTime = LocalDateTime.now(),
    orderType: PoetOrderType = PoetOrderType.LOCAL,
    quoteId: Long = *********,
    totalOrderPriceCents: Long = 100,
    rentalAgreementSigned: Boolean = true,
    containers: List<PoetContainer> = listOf(createPoetContainer()),
    status: OrderStatus = OrderStatus.BOOKED,
    billingCompanyCode: String = "PEIU",
) = PoetOrder(
    orderId = orderId,
    orderDate = orderDate,
    orderType = orderType,
    quoteId = quoteId,
    totalOrderPriceCents = totalOrderPriceCents,
    rentalAgreementSigned = rentalAgreementSigned,
    containers = containers,
    status = status,
    billingCompanyCode = billingCompanyCode,
)

fun createPoetContainer(
    containerId: String? = "1",
    containerOrderId: String = "*********",
    containerSize: PoetContainerSize = PoetContainerSize.CONTAINER_SIZE_12,
    orderOriginCompanyCode: String = "ABC123",
    hasCityServiceMoveLeg: Boolean = false,
    moveLegs: List<PoetMoveLeg> = listOf(createPoetMoveLeg()),
    status: ContainerStatus = ContainerStatus.BOOKED,
) = PoetContainer(
    containerId = containerId,
    containerOrderId = containerOrderId,
    containerSize = containerSize,
    orderOriginCompanyCode = orderOriginCompanyCode,
    hasCityServiceMoveLeg = hasCityServiceMoveLeg,
    moveLegs = moveLegs,
    status = status,
)

fun createPoetMoveLeg(
    moveLegId: String = "1",
    moveLegType: MoveLegType = MoveLegType.INITIAL_DELIVERY,
    transitDays: Int = 1,
    scheduledDate: LocalDate? = LocalDate.now(),
    status: OrderStatus = OrderStatus.BOOKED,
    isCityService: Boolean = false,
    isHawaii: Boolean = false,
    isCrossBorder: Boolean = false,
    dayOfEtaWindow: String? = null,
    siteIdentity: String = "PME2",
    originationAddress: PoetMoveLegAddress = createPoetMoveLegAddress(),
    destinationAddress: PoetMoveLegAddress = createPoetMoveLegAddress(),
    moveNumber: String = "moveNumber",
) = PoetMoveLeg(
    moveLegId = moveLegId,
    moveLegType = moveLegType,
    transitDays = transitDays,
    scheduledDate = scheduledDate,
    status = status,
    isCityService = isCityService,
    isHawaii = isHawaii,
    isCrossBorder = isCrossBorder,
    dayOfEtaWindow = dayOfEtaWindow,
    siteIdentity = siteIdentity,
    originationAddress = originationAddress,
    destinationAddress = destinationAddress,
    moveNumber = moveNumber,
)

fun createPoetMoveLegAddress(
    address1: String = "123 Chicago Ave",
    address2: String = "Unit 1",
    city: String = "Chicago",
    state: String = "IL",
    postalCode: String = "12345",
    country: String = "US",
    isStorageCenter: Boolean = false,
): PoetMoveLegAddress =
    PoetMoveLegAddress(
        address1 = address1,
        address2 = address2,
        city = city,
        state = state,
        postalCode = postalCode,
        country = country,
        isStorageCenter = isStorageCenter,
    )

fun createPoetAddress(
    street: String = "123 Chicago Ave",
    city: String = "Chicago",
    state: String = "IL",
    zipCode: String = "12345",
    isPrimary: Boolean = true,
    countryCode: String = "US",
): PoetAccountAddress =
    PoetAccountAddress(
        city = city,
        countryCode = countryCode,
        isPrimary = isPrimary,
        zipCode = zipCode,
        state = state,
        street = street,
    )

fun createPoetDocument(
    docRef: String = "fakeDocRef",
    docName: String = "fakeDocName",
    isCustomerFacing: Boolean = true,
    id: String = "fakeId",
    docType: PoetDocumentType = PoetDocumentType.RENTAL_AGREEMENT,
    orderId: String = "12345",
    tags: String? = null,
    docNotes: String = "test-doc-Notes",
    invoiceNumber: String = "321",
): PoetDocument =
    PoetDocument(
        docRef = docRef,
        docName = docName,
        isCustomerFacing = isCustomerFacing,
        id = id,
        docType = docType,
        orderId = orderId,
        tags = tags,
        docNotes = docNotes,
        invoiceNumber = invoiceNumber,
    )

fun createCreditLine(
    applicationId: Int = 1,
    balance: Double = 200.0,
    initialLoanAmount: Double = 1000.0,
    originationDate: LocalDateTime = LocalDateTime.parse("2024-04-23T07:00:18"),
    terminationDate: LocalDateTime = LocalDateTime.parse("2024-04-23T07:00:18"),
    isPrimary: Boolean = true,
    isActive: Boolean = true,
    lender: String = "expected-credit-lender",
    providerName: String = "expected-credit-provider",
    createdBy: String = "other-user",
    createdDateTime: LocalDateTime = LocalDateTime.parse("2024-04-23T07:00:18"),
    modifiedBy: String = "other-user",
    modifiedDateTime: LocalDateTime = LocalDateTime.parse("2024-04-23T07:00:18"),
) = PoetCreditLine(
    applicationId = applicationId,
    balance = balance,
    initialLoanAmount = initialLoanAmount,
    originationDate = originationDate,
    terminationDate = terminationDate,
    isPrimary = isPrimary,
    isActive = isActive,
    lender = lender,
    providerName = providerName,
    createdBy = createdBy,
    createdDateTime = createdDateTime,
    modifiedBy = modifiedBy,
    modifiedDateTime = modifiedDateTime,
)

fun createBillingAddress(
    street: String = "expected-address-street",
    city: String = "expected-address-city",
    state: String = "expected-address-state",
    country: String = "expected-address-country",
    zipCode: String = "expected-address-zip-code",
    role: String = "expected-address-role",
    isPrimary: Boolean = true,
    isActive: Boolean = true,
    recId: Long = 0L,
) = PoetBillingAddressResponse(
    street = street,
    city = city,
    state = state,
    country = country,
    zipCode = zipCode,
    role = role,
    isPrimary = isPrimary,
    isActive = isActive,
    recId = recId,
)

fun createCreditCard(
    creditCardId: String = "1",
    creditCardNum: String = "1234**********1234",
    creditCardNumSecure: String = "1234**********1234",
    creditCardProfileId: String = "expected-credit-card-profile-id",
    podsCardBrandTxnId: String = "expected-pods-card-brand-txn-id",
    expirationDate: LocalDateTime = LocalDateTime.parse("2024-04-23T07:00:18"),
    nameOnCard: String = "Joe Smith",
    creditCardType: String = "expected-credit-lender",
    isPrimary: Boolean = true,
    isActive: Boolean = true,
    createdBy: String = "other-user",
    createdDateTime: LocalDateTime = LocalDateTime.parse("2024-04-23T07:00:18"),
    modifiedBy: String = "other-user",
    modifiedDateTime: LocalDateTime = LocalDateTime.parse("2024-04-23T07:00:18"),
    billingAddress: PoetBillingAddressResponse? = createBillingAddress(),
) = PoetCreditCard(
    creditCardId = creditCardId,
    creditCardNum = creditCardNum,
    creditCardNumSecure = creditCardNumSecure,
    creditCardProfileId = creditCardProfileId,
    podsCardBrandTxnId = podsCardBrandTxnId,
    expirationDate = expirationDate,
    nameOnCard = nameOnCard,
    creditCardType = creditCardType,
    isPrimary = isPrimary,
    isActive = isActive,
    createdBy = createdBy,
    createdDateTime = createdDateTime,
    modifiedBy = modifiedBy,
    modifiedDateTime = modifiedDateTime,
    billingAddress = billingAddress,
)

fun createPoetInvoice(
    invoice: String = "invoiceId",
    description: String = "description",
    dueDate: ZonedDateTime = ZonedDateTime.now(),
    invoiceAmount: Float = 1000.0F,
    remainingBalance: Float = 1000.0F,
    settledAmount: Float = 0.0F,
    currency: Currency = Currency.USD,
) = PoetInvoice(
    invoice = invoice,
    description = description,
    dueDate = dueDate,
    invoiceAmount = invoiceAmount,
    remainingBalance = remainingBalance,
    settledAmount = settledAmount,
    currency = currency,
)

fun createPoetServiceabilityResponse(
    callInfo: PoetCallInfo = PoetCallInfo("OK", ""),
    postalCodeException: PoetPostalCodeException = PoetPostalCodeException("", "", "", ""),
    servicingLocations: List<PoetServicingLocation> = emptyList(),
) = PoetServiceabilityResponse(
    callInfo = callInfo,
    postalCodeException = postalCodeException,
    servicingLocations = servicingLocations,
)

fun createGenerateOrderChangesRequest(
    orderId: String = "orderId",
    containerOrderId: String = "containerOrderId",
    requestedDate: LocalDate = LocalDate.now(),
    moveLegId: String = "moveLegId",
    requestedServiceAddress: PoetMoveLegAddress? = null,
    isRequestedServiceForOrigin: Boolean = false,
    containerPlacement: ContainerPlacement? = null,
    locationAvailabilityFields: PoetLocationAvailabilityFields = createPoetLocationAvailabilityFields(),
): GenerateOrderChangesRequest = GenerateOrderChangesRequest(
    orderId = orderId,
    containerOrderId = containerOrderId,
    requestedDate = requestedDate,
    moveLegId = moveLegId,
    requestedServiceAddress = requestedServiceAddress,
    isRequestedServiceForOrigin = isRequestedServiceForOrigin,
    containerPlacement = containerPlacement,
    locationAvailabilityFields = locationAvailabilityFields,
)

fun createPoetLocationAvailabilityFields(
    zipCode: String = "12345",
    moveLegType: MoveLegType = MoveLegType.INITIAL_DELIVERY,
    orderType: PoetOrderType = PoetOrderType.LOCAL,
    siteIdentity: String = "PME2",
    isIfOpenCalendar: Boolean = false,
) = PoetLocationAvailabilityFields(
    zipCode = zipCode,
    moveLegType = moveLegType,
    orderType = orderType,
    siteIdentity = siteIdentity,
    isIfOpenCalendar = isIfOpenCalendar,
)
