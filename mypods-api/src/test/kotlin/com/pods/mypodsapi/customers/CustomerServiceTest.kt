package com.pods.mypodsapi.customers

import com.pods.mypodsapi.customers.exceptions.UpdateEmailException
import com.pods.mypodsapi.customers.exceptions.UpdatePasswordException
import com.pods.mypodsapi.customers.exceptions.VerifyChallengeException
import com.pods.mypodsapi.external.customerAccounts.CustomerAccountsClient
import com.pods.mypodsapi.external.customerAccounts.CustomerAccountsClientConfig
import com.pods.mypodsapi.external.customerAccounts.createCAUpdatePasswordResponse
import com.pods.mypodsapi.external.customerAccounts.createCustomerAccountsResponseMessage
import com.pods.mypodsapi.external.customerAccounts.entities.CAUpdatePasswordResponseCode.INVALID_CREDENTIALS
import com.pods.mypodsapi.external.customerAccounts.entities.CAUpdateUsernameResponse
import com.pods.mypodsapi.external.customerAccounts.entities.CAUpdateUsernameResponseCode.INVALID_EMAIL
import com.pods.mypodsapi.external.customerAccounts.entities.CAUpdateUsernameResponseCode.SUCCESS_VERIFICATION_SENT
import com.pods.mypodsapi.external.customerAccounts.entities.CAVerifyChallengeResponse
import com.pods.mypodsapi.external.customerAccounts.entities.CAVerifyChallengeResponseCode
import com.pods.mypodsapi.external.customerAccounts.toCustomerAccountsRequest
import com.pods.mypodsapi.external.locum.LocumClient
import com.pods.mypodsapi.external.locum.LocumClientConfig
import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import com.pods.mypodsapi.external.poetFacade.createPoetCustomer
import com.pods.mypodsapi.external.poetFacade.entities.PoetAccountPhone
import com.pods.mypodsapi.external.poetFacade.mappers.toCustomer
import com.pods.mypodsapi.external.poetFacade.mappers.toUpdateAccountRequest
import com.pods.mypodsapi.session.createSessionCookieValues
import com.pods.mypodsapi.testUtils.ClientTestBase
import com.pods.mypodsapi.testUtils.WireMockFixtures.stubAnyPostJsonResponse
import com.pods.mypodsapi.testUtils.WireMockFixtures.stubAnyPutJsonResponse
import com.pods.mypodsapi.testUtils.myPodsObjectMapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import reactor.core.publisher.Mono
import strikt.api.expectThat
import strikt.api.expectThrows
import strikt.assertions.isEqualTo
import java.util.*

class CustomerServiceTest : ClientTestBase() {
    private val mockPoetClient = mockk<PoetFacadeClient>()
    private val mockCustomerAccountsClient = mockk<CustomerAccountsClient>()
    private val objectMapper = myPodsObjectMapper()
    private val customerService = CustomerService(
        mockCustomerAccountsClient,
        objectMapper,
        mockPoetClient,
    )

    // Specific customerId that does not return fake date when poet facade is enabled
    private val customerId = "*********"
    private val trackingUuid = UUID.randomUUID().toString()
    private val username = "<EMAIL>"

    @Nested
    inner class GetCustomerByCustomerId {
        private val poetCustomer = createPoetCustomer()

        @Test
        fun `should map poet customer`() =
            runTest {
                every { mockPoetClient.getCustomer(any()) } returns Mono.just(poetCustomer)

                val result = customerService.getCustomer(customerId, trackingUuid, username)

                val expected = poetCustomer.toCustomer(trackingUuid, username)
                expectThat(result).isEqualTo(expected)
                verify { mockPoetClient.getCustomer(customerId) }
            }

        @Test
        fun `should format the phoneNumber to xxx-xxx-xxxx`() =
            runTest {
                val expectedPhone = "************"
                every { mockPoetClient.getCustomer(any()) } returns
                        Mono.just(poetCustomer.copy(primaryPhone = PoetAccountPhone("**********")))

                val result = customerService.getCustomer(customerId, trackingUuid, username)

                expectThat(result.primaryPhone!!.number).isEqualTo(expectedPhone)
            }
    }

    @Nested
    inner class UpdatePrimaryPhone {
        private val updateRequest = createUpdatePrimaryPhoneRequest()

        @BeforeEach
        fun setup() {
            every {
                mockPoetClient.updateAccount(any(), any())
            } returns Mono.empty()
        }

        @Test
        fun `should remove dashes from phone number when updating phone number via POET`() {
            runTest {
                customerService.updatePrimaryPhone(customerId, updateRequest)
                val expectedPhoneNumberRequest = UpdateAccountRequest(primaryPhoneNumber = "**********")
                verify {
                    mockPoetClient.updateAccount(customerId, expectedPhoneNumberRequest)
                }
            }
        }
    }

    @Nested
    inner class UpdateSecondaryPhone {
        private val updateRequest = createUpdateSecondaryPhoneRequest()

        @BeforeEach
        fun setup() {
            every {
                mockPoetClient.updateAccount(any(), any())
            } returns Mono.empty()
        }

        @Test
        fun `should remove dashes from phone number when updating phone number`() {
            runTest {
                customerService.updateSecondaryPhone(customerId, updateRequest)
                val expectedPhoneNumberRequest = UpdateAccountRequest(secondaryPhoneNumber = "**********")
                verify {
                    mockPoetClient.updateAccount(customerId, expectedPhoneNumberRequest)
                }
            }
        }
    }

    @Nested
    inner class UpdateBillingAddress {
        private val updateRequest = createUpdateBillingAddressRequest()

        @BeforeEach
        fun setup() {
            every { mockPoetClient.updateAccount(any(), any()) } returns Mono.empty()
        }

        @Test
        fun `should update billing address using poet if feature flag is enabled`() =
            runTest {
                customerService.updateBillingAddress(customerId, updateRequest)

                verify {
                    mockPoetClient.updateAccount(
                        customerId,
                        updateRequest.toUpdateAccountRequest(),
                    )
                }
            }
    }

    @Nested
    inner class UpdateShippingAddress {
        private val updateRequest = createUpdateShippingAddressRequest()

        @BeforeEach
        fun setup() {
            every { mockPoetClient.updateAccount(any(), any()) } returns Mono.empty()
        }

        @Test
        fun `should update shipping address using poet if feature flag is enabled`() =
            runTest {
                customerService.updateShippingAddress(customerId, updateRequest)

                verify {
                    mockPoetClient.updateAccount(
                        customerId,
                        updateRequest.toUpdateAccountRequest(),
                    )
                }
            }
    }

    @Nested
    inner class UpdateSmsOptIn {
        private val customerId = "*********"

        @BeforeEach
        fun setup() {
            every {
                mockPoetClient.updateAccount(any(), any())
            } returns Mono.empty()
        }

        @Test
        fun `if sending to poet, send true if enabling sms`() =
            runTest {
                val request =
                    createUpdateSmsOptInRequest(
                        primaryPhone = null,
                        secondaryPhone = createPhone(),
                        newSmsOptIn = true,
                    )

                customerService.updateSmsOptIn(customerId, request)

                verify { mockPoetClient.updateAccount(customerId, UpdateAccountRequest(smsOptIn = true)) }
            }

        @Test
        fun `if sending to poet, send false if disabling sms`() =
            runTest {
                val request =
                    createUpdateSmsOptInRequest(
                        primaryPhone = null,
                        secondaryPhone = createPhone(),
                        newSmsOptIn = false,
                    )

                customerService.updateSmsOptIn(customerId, request)

                verify { mockPoetClient.updateAccount(customerId, UpdateAccountRequest(smsOptIn = false)) }
            }
    }

    // When testing features that make an api call, and need to handle a non 2xx status code response
    // It is much easier to get wiremock to return a different status code, then to mock the client to throw WebClientResponseException
    @Nested
    inner class WireMockTests {
        private lateinit var locumClient: LocumClient
        private lateinit var customerAccountsClient: CustomerAccountsClient
        private lateinit var customerService: CustomerService

        @BeforeEach
        fun setup() {
            locumClient = LocumClientConfig(baseUrl).locumClient(webClient())
            customerAccountsClient =
                CustomerAccountsClientConfig(
                    baseUrl,
                    "apiKey",
                ).customerAccountsClient(webClient())
            customerService =
                CustomerService(
                    customerAccountsClient,
                    objectMapper,
                    mockPoetClient,
                )
        }

        @Nested
        inner class UpdateEmail {
            private val headers = HttpHeaders()
            private val cookieValues = createSessionCookieValues(headers)
            private val updateRequest = createUpdateEmailRequest(createEmail())
            private val responseMessage = createCustomerAccountsResponseMessage()
            private val successResponse = CAUpdateUsernameResponse("", SUCCESS_VERIFICATION_SENT, responseMessage)

            @BeforeEach
            fun setup() {
                every {
                    mockCustomerAccountsClient.updateUsername(any(), any())
                } returns Mono.just(successResponse)
            }

            @Test
            fun `should update email`() =
                runTest {
                    <EMAIL>(
                        updateRequest,
                        cookieValues
                    )

                    verify {
                        mockCustomerAccountsClient.updateUsername(
                            updateRequest.toCustomerAccountsRequest(),
                            headers,
                        )
                    }
                }

            @Test
            fun `should throw an exception if CustomerAccounts returns a non-SUCCESS response code`() =
                runTest {
                    val failureResponse = CAUpdateUsernameResponse("", INVALID_EMAIL, responseMessage)
                    every {
                        mockCustomerAccountsClient.updateUsername(any(), any())
                    } returns Mono.just(failureResponse)

                    expectThrows<UpdateEmailException> {
                        <EMAIL>(
                            updateRequest,
                            cookieValues,
                        )
                    }
                }

            @Test
            fun `should convert to pods exception if customer accounts returns unsuccessful http status`() =
                runTest {
                    val failureResponse = CAUpdateUsernameResponse("", INVALID_EMAIL, responseMessage)
                    stubAnyPutJsonResponse(
                        objectMappper.writeValueAsString(failureResponse),
                        HttpStatus.INTERNAL_SERVER_ERROR,
                    )

                    expectThrows<UpdateEmailException> {
                        customerService.updateEmail(updateRequest, cookieValues)
                    }
                }
        }

        @Nested
        inner class UpdatePassword {
            private val request = createUpdatePasswordRequest()
            private val headers = HttpHeaders()
            private val cookieValues = createSessionCookieValues(headers)
            private val successResponse = createCAUpdatePasswordResponse()

            @BeforeEach
            fun setup() {
                every {
                    mockCustomerAccountsClient.updatePassword(any(), any())
                } returns Mono.just(successResponse)
            }

            @Test
            fun `should request customer accounts to update password`() =
                runTest {
                    <EMAIL>(request, cookieValues)

                    verify { mockCustomerAccountsClient.updatePassword(request.toCustomerAccountsRequest(), headers) }
                }

            @Test
            fun `should throw an exception if CustomerAccounts returns a non-SUCCESS response code`() =
                runTest {
                    val failureResponse = createCAUpdatePasswordResponse(responseCode = INVALID_CREDENTIALS)
                    every {
                        mockCustomerAccountsClient.updatePassword(any(), any())
                    } returns Mono.just(failureResponse)

                    expectThrows<UpdatePasswordException> {
                        <EMAIL>(request, cookieValues)
                    }
                }

            @Test
            fun `should convert to pods exception if customer accounts returns unsuccessful http status`() =
                runTest {
                    val failureResponse = createCAUpdatePasswordResponse(INVALID_CREDENTIALS)
                    stubAnyPutJsonResponse(
                        objectMappper.writeValueAsString(failureResponse),
                        HttpStatus.INTERNAL_SERVER_ERROR,
                    )

                    expectThrows<UpdatePasswordException> {
                        customerService.updatePassword(request, cookieValues)
                    }
                }
        }

        @Nested
        inner class VerifyChallengeRequestTest {
            private val headers = HttpHeaders()
            private val cookieValues = createSessionCookieValues(headers)
            private val verifyRequest = VerifyChallengeRequest("1234")
            private val successResponse = CAVerifyChallengeResponse(CAVerifyChallengeResponseCode.SUCCESS)

            @BeforeEach
            fun setup() {
                every {
                    mockCustomerAccountsClient.verifyChallenge(any(), any())
                } returns Mono.just(successResponse)
            }

            @Test
            fun `sends the four-digit one time password to customer accounts`() =
                runTest {
                    <EMAIL>(verifyRequest, cookieValues)

                    verify {
                        mockCustomerAccountsClient.verifyChallenge(
                            verifyRequest.toCAVerifyChallengeRequest(),
                            headers
                        )
                    }
                }

            @Test
            fun `should throw an exception if CustomerAccounts returns a non-SUCCESS response code`() =
                runTest {
                    val failureResponse = CAVerifyChallengeResponse(CAVerifyChallengeResponseCode.INVALID_CODE)
                    stubAnyPostJsonResponse(
                        objectMappper.writeValueAsString(failureResponse),
                        HttpStatus.INTERNAL_SERVER_ERROR,
                    )

                    expectThrows<VerifyChallengeException> {
                        customerService.verifyChallenge(
                            verifyRequest,
                            cookieValues,
                        )
                    }
                }
        }
    }
}
