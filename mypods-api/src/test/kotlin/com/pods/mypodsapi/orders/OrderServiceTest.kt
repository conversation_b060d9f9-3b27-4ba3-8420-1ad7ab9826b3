package com.pods.mypodsapi.orders

import com.pods.mypodsapi.PodsException
import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import com.pods.mypodsapi.external.poetFacade.createPoetContainer
import com.pods.mypodsapi.external.poetFacade.createPoetDocument
import com.pods.mypodsapi.external.poetFacade.createPoetMoveLeg
import com.pods.mypodsapi.external.poetFacade.createPoetOrder
import com.pods.mypodsapi.external.poetFacade.createPoetServiceabilityResponse
import com.pods.mypodsapi.external.poetFacade.entities.ApplyOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.ApplyQuoteToOrderResponse
import com.pods.mypodsapi.external.poetFacade.entities.ContainerStatus
import com.pods.mypodsapi.external.poetFacade.entities.GenerateQuoteForOrderChangesResponse
import com.pods.mypodsapi.external.poetFacade.entities.OrderStatus
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrderType
import com.pods.mypodsapi.external.poetFacade.entities.PoetServicingLocation
import com.pods.mypodsapi.external.poetFacade.entities.Timing
import com.pods.mypodsapi.external.poetFacade.entities.WarehouseId
import com.pods.mypodsapi.external.poetFacade.mappers.toOrder
import com.pods.mypodsapi.external.poetFacade.mappers.toPoetRequest
import com.pods.mypodsapi.external.poetFacade.toGenerateOrderChangesRequest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import reactor.core.publisher.Mono
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.isFalse
import strikt.assertions.isNull
import strikt.assertions.isTrue
import java.time.LocalDate

class OrderServiceTest {
    private val mockPoetFacadeClient = mockk<PoetFacadeClient>()
    private val warehouseHoursService = WarehouseHoursService(mockPoetFacadeClient)
    private val orderService = OrderService(mockPoetFacadeClient, warehouseHoursService)
    private val customerId = "111222333"

    @Nested
    inner class GetOrdersByCustomerId {
        private val activeContainer = createPoetContainer(status = ContainerStatus.BOOKED)
        private val bookedOrder =
            createPoetOrder(
                orderId = "booked",
                status = OrderStatus.BOOKED,
                containers = listOf(activeContainer),
            )
        private val warehouseId1 = "test-warehouseId-1"
        private val warehouseId2 = "test-warehouseId-2"
        private val expectedStartTime = "10:00AM"
        private val expectedEndTime = "5:00PM"
        private val warehouseTimingsResponse =
            listOf(
                mapOf(
                    this.warehouseId1 to
                        mapOf(
                            java.time.DayOfWeek.SUNDAY to Timing(startTime = "Closed", endTime = "Closed"),
                            java.time.DayOfWeek.MONDAY to Timing(startTime = "8:00AM", endTime = "3:00PM"),
                            java.time.DayOfWeek.FRIDAY to
                                Timing(
                                    startTime = expectedStartTime,
                                    endTime = expectedEndTime,
                                ),
                        ),
                ),
            )

        @BeforeEach
        fun setUp() {
            every { mockPoetFacadeClient.getWarehousesTimings() } returns Mono.just(warehouseTimingsResponse)
        }

        @Test
        fun `returns orders from poet response`() =
            runTest {
                val orders = listOf(createPoetOrder())
                every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                val result = orderService.getOrdersByCustomerId(customerId)

                expectThat(result).isEqualTo(orders.map { it.toOrder() })
                verify { mockPoetFacadeClient.getOrdersByCustomerId(customerId) }
            }

        @Test
        fun `filters out orders with all cancelled containers`() {
            runTest {
                val orderWithCancelledContainer =
                    createPoetOrder(
                        orderId = "orderWithCancelledContainer",
                        status = OrderStatus.BOOKED,
                        containers = listOf(createPoetContainer(status = ContainerStatus.CANCELLED)),
                    )
                val orders = listOf(bookedOrder, orderWithCancelledContainer)
                every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                val result = orderService.getOrdersByCustomerId(customerId)

                expectThat(result.size).isEqualTo(1)
                val firstOrder = result.first()
                expectThat(firstOrder.orderId).isEqualTo(bookedOrder.orderId)
                expectThat(firstOrder.containers.size).isEqualTo(1)
                expectThat(firstOrder.containers.firstOrNull()?.containerId)
                    .isEqualTo(activeContainer.containerId)
            }
        }

        @Test
        fun `filters out containers with all cancelled moveLegs`() {
            runTest {
                val orderWithCancelledMoveLegs =
                    createPoetOrder(
                        orderId = "orderWithCancelledMoveLegs",
                        status = OrderStatus.BOOKED,
                        containers =
                            listOf(
                                createPoetContainer(
                                    status = ContainerStatus.BOOKED,
                                    moveLegs = listOf(createPoetMoveLeg(status = OrderStatus.CANCELLED)),
                                ),
                            ),
                    )
                val orders = listOf(bookedOrder, orderWithCancelledMoveLegs)
                every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                val result = orderService.getOrdersByCustomerId(customerId)

                expectThat(result.size).isEqualTo(1)
                expectThat(result.first().orderId).isEqualTo(bookedOrder.orderId)
                expectThat(result.find { it.orderId == orderWithCancelledMoveLegs.orderId }).isNull()
            }
        }

        @Test
        fun `filters out cancelled orders`() {
            runTest {
                val orders =
                    listOf(
                        bookedOrder,
                        createPoetOrder(orderId = "cancelled", status = OrderStatus.CANCELLED),
                    )
                every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                val result = orderService.getOrdersByCustomerId(customerId)

                expectThat(result.size).isEqualTo(1)
                expectThat(result.first().orderId).isEqualTo(bookedOrder.orderId)
            }
        }

        @Test
        fun `filters out orders with no active containers`() {
            runTest {
                val orderWithNoContainers =
                    createPoetOrder(
                        orderId = "orderWithNoContainers",
                        status = OrderStatus.BOOKED,
                        containers = emptyList(),
                    )
                val orders =
                    listOf(
                        bookedOrder,
                        orderWithNoContainers,
                    )
                every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                val result = orderService.getOrdersByCustomerId(customerId)

                expectThat(result.size).isEqualTo(1)
                expectThat(result.first().orderId).isEqualTo(bookedOrder.orderId)
            }
        }

        @Test
        fun `gets warehouse timing for a scheduled ACS move leg`() =
            runTest {
                val scheduledAcsMoveleg =
                    createPoetMoveLeg(
                        moveLegType = MoveLegType.VISIT_CONTAINER,
                        scheduledDate = LocalDate.parse("2024-02-02"),
                        siteIdentity = this@GetOrdersByCustomerId.warehouseId1,
                    )
                val containers =
                    listOf(
                        createPoetContainer(
                            status = ContainerStatus.BOOKED,
                            moveLegs = listOf(scheduledAcsMoveleg),
                        ),
                    )
                val orders =
                    listOf(createPoetOrder(containers = containers))
                every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                val result = orderService.getOrdersByCustomerId(customerId)

                expectThat(
                    result
                        .first()
                        .containers
                        .first()
                        .moveLegs
                        .first()
                        .eta,
                ).isEqualTo("$expectedStartTime - $expectedEndTime")
            }

        @Test
        fun `if we are unable to get warehouse timings, do not change existing movelegs`() =
            runTest {
                every { mockPoetFacadeClient.getWarehousesTimings() } throws (PodsException.notFound())
                val scheduledAcsMoveleg =
                    createPoetMoveLeg(
                        moveLegType = MoveLegType.VISIT_CONTAINER,
                        scheduledDate = LocalDate.parse("2024-02-02"),
                        siteIdentity = this@GetOrdersByCustomerId.warehouseId1,
                    )
                val containers =
                    listOf(
                        createPoetContainer(
                            status = ContainerStatus.BOOKED,
                            moveLegs = listOf(scheduledAcsMoveleg),
                        ),
                    )
                val orders =
                    listOf(createPoetOrder(containers = containers))
                every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                val result = orderService.getOrdersByCustomerId(customerId)

                expectThat(
                    result,
                ).isEqualTo(orders.map { it.toOrder() })
            }

        @Test
        fun `gets warehouse timing for a scheduled SELF_PICKUP move leg`() =
            runTest {
                val scheduledAcsMoveleg =
                    createPoetMoveLeg(
                        moveLegType = MoveLegType.SELF_FINAL_PICKUP,
                        scheduledDate = LocalDate.parse("2024-02-02"),
                        siteIdentity = this@GetOrdersByCustomerId.warehouseId2,
                    )
                val containers =
                    listOf(
                        createPoetContainer(
                            status = ContainerStatus.BOOKED,
                            moveLegs = listOf(scheduledAcsMoveleg),
                        ),
                    )
                val orders =
                    listOf(createPoetOrder(containers = containers))
                every { mockPoetFacadeClient.getOrdersByCustomerId(any()) } returns Mono.just(orders)

                val result = orderService.getOrdersByCustomerId(customerId)

                expectThat(
                    result
                        .first()
                        .containers
                        .first()
                        .moveLegs
                        .first()
                        .eta,
                ).isNull()
            }
    }

    @Nested
    inner class GetOrderDocumentsForCustomer {
        private val incompletePoetDocument = createPoetDocument(tags = "Sent")
        private val completePoetDocument = createPoetDocument(tags = "Completed")

        private val poetDocuments = listOf(incompletePoetDocument, completePoetDocument)

        @Test
        fun `given poet documents, converts them to domain objects`() =
            runTest {
                every { mockPoetFacadeClient.getOrderDocuments(any()) } returns Mono.just(poetDocuments)

                val documents = orderService.getOrderDocumentsForCustomer("cuid")

                expectThat(documents).isEqualTo(
                    listOf(
                        incompletePoetDocument.toDocument(),
                        completePoetDocument.toDocument(),
                    ),
                )
            }
    }

    @Nested
    inner class UpdateMoveLeg {
        private val request =
            createUpdateMoveLegRequest(
                containerOrderId = "testContainerOrderId",
                requestedDate = LocalDate.now().plusDays(10),
            )
        private val sfQuoteId = "12345"
        private val zeroPriceDifference = 0L
        private val generateQuoteWithZeroPriceDiff =
            GenerateQuoteForOrderChangesResponse(
                sfQuoteId = sfQuoteId,
                priceDifference = zeroPriceDifference,
            )
        private val nonZeroPriceDifference = 111L
        private val generateQuoteWithPriceDiff =
            GenerateQuoteForOrderChangesResponse(
                sfQuoteId = sfQuoteId,
                priceDifference = nonZeroPriceDifference,
            )

        @Test
        fun `should generate quote and apply it to order when priceDiff is 0L`() =
            runTest {
                val applyQuoteResponse = ApplyQuoteToOrderResponse(sfQuoteId = sfQuoteId)
                every { mockPoetFacadeClient.generateOrderChanges(any()) } returns
                    Mono.just(
                        generateQuoteWithZeroPriceDiff,
                    )
                every { mockPoetFacadeClient.applyQuoteToOrder(any()) } returns Mono.just(applyQuoteResponse)

                val updateMoveLegResponse = orderService.updateMoveLeg(request)

                verify { mockPoetFacadeClient.generateOrderChanges(request.toGenerateOrderChangesRequest()) }
                verify { mockPoetFacadeClient.applyQuoteToOrder(ApplyOrderChangesRequest(sfQuoteId)) }
                expectThat(updateMoveLegResponse.quoteId).isNull()
                expectThat(updateMoveLegResponse.priceDifference).isNull()
            }

        @Test
        fun `should generate quote and SKIP applying it to the order when priceDiff is NOT 0L`() =
            runTest {
                val expectedPriceDiffInPercentage = String.format("%.2f", nonZeroPriceDifference / 100.0)
                every { mockPoetFacadeClient.generateOrderChanges(any()) } returns Mono.just(generateQuoteWithPriceDiff)

                val updateMoveLegResponse = orderService.updateMoveLeg(request)

                verify { mockPoetFacadeClient.generateOrderChanges(request.toGenerateOrderChangesRequest()) }
                verify(exactly = 0) { mockPoetFacadeClient.applyQuoteToOrder(ApplyOrderChangesRequest(sfQuoteId)) }
                expectThat(updateMoveLegResponse.priceDifference).isEqualTo(expectedPriceDiffInPercentage)
                expectThat(updateMoveLegResponse.quoteId).isEqualTo(sfQuoteId)
            }
    }

    @Nested
    inner class IsSameServiceArea {
        val request = createSameServiceAreaRequest()

        @Test
        fun `returns true if the warehouse IDs match for origination and destination`() =
            runTest {
                val poetResponse =
                    createPoetServiceabilityResponse(
                        servicingLocations =
                            listOf(
                                PoetServicingLocation(
                                    isSelected = true,
                                    orderType = PoetOrderType.LOCAL,
                                    origination = WarehouseId("test-warehouse-id"),
                                    destination = WarehouseId("test-warehouse-id"),
                                ),
                            ),
                    )
                every { mockPoetFacadeClient.getServiceability(any()) } returns Mono.just(poetResponse)

                val result = orderService.isSameServiceArea(request)

                expectThat(result).isTrue()
                verify { mockPoetFacadeClient.getServiceability(request.toPoetRequest()) }
            }

        @Test
        fun `returns false if the warehouse IDs DO NOT match for origination and destination`() =
            runTest {
                val poetResponse =
                    createPoetServiceabilityResponse(
                        servicingLocations =
                            listOf(
                                PoetServicingLocation(
                                    isSelected = true,
                                    orderType = PoetOrderType.LOCAL,
                                    origination = WarehouseId("test-warehouse-id"),
                                    destination = WarehouseId("other-warehouse-id"),
                                ),
                            ),
                    )
                every { mockPoetFacadeClient.getServiceability(any()) } returns Mono.just(poetResponse)

                val result = orderService.isSameServiceArea(request)

                expectThat(result).isFalse()
                verify { mockPoetFacadeClient.getServiceability(request.toPoetRequest()) }
            }
    }
}
