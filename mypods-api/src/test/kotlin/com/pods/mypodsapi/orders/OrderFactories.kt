package com.pods.mypodsapi.orders

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

@Suppress("LongParameterList")
fun createOrder(
    orderId: String = "orderId",
    orderType: OrderType = OrderType.LOCAL,
    quoteId: Long = 123,
    rentalAgreementSigned: Boolean = true,
    price: Double? = null,
    orderDate: LocalDateTime = LocalDateTime.now(),
    containers: List<Container> = listOf(createContainer()),
    originationCompanyCode: String? = null,
): Order =
    Order(
        orderId = orderId,
        orderType = orderType,
        quoteId = quoteId,
        price = price,
        orderDate = orderDate,
        rentalAgreementSigned = rentalAgreementSigned,
        containers = containers,
        billingCompanyCode = originationCompanyCode,
    )

fun createContainer(
    containerId: String = "containerId",
    containerType: String = "containerType",
    companyCode: String = "companyCode",
    hasCityServiceMoves: Boolean = false,
    moveLegs: List<MoveLeg> = listOf(createMoveLeg()),
): Container =
    Container(
        containerId = containerId,
        containerType = containerType,
        companyCode = companyCode,
        hasCityServiceMoves = hasCityServiceMoves,
        moveLegs = moveLegs,
    )

fun createMoveLeg(
    moveLegId: String = "moveLegId",
    moveLegType: MoveLegType = MoveLegType.INITIAL_DELIVERY,
    isCityService: Boolean = false,
    isHawaii: Boolean = false,
    isCrossBorder: Boolean = false,
    scheduledDate: LocalDate? = LocalDate.now(),
    eta: String? = null,
    transitDays: Int = 0,
    siteIdentity: String = "PME2",
    originationAddress: MoveLegAddress = createMoveLegAddress(),
    destinationAddress: MoveLegAddress = createMoveLegAddress(),
): MoveLeg =
    MoveLeg(
        moveLegId = moveLegId,
        moveLegType = moveLegType,
        isCityService = isCityService,
        isHawaii = isHawaii,
        isCrossBorder = isCrossBorder,
        scheduledDate = scheduledDate,
        eta = eta,
        transitDays = transitDays,
        siteIdentity = siteIdentity,
        originationAddress = originationAddress,
        destinationAddress = destinationAddress,
    )

@Suppress("LongParameterList")
fun createMoveLegAddress(
    address1: String = "123 Chicago Ave",
    address2: String = "Unit 1",
    city: String = "Chicago",
    state: String = "IL",
    postalCode: String = "12345",
    country: String = "US",
    isStorageCenter: Boolean = false,
): MoveLegAddress =
    MoveLegAddress(
        address1 = address1,
        address2 = address2,
        city = city,
        state = state,
        postalCode = postalCode,
        country = country,
        isStorageCenter = isStorageCenter,
    )

fun createUpdateMoveLegRequest(
    orderId: String = "orderId",
    containerOrderId: String? = null,
    quoteId: Long = 1,
    moveLegId: String = "123SP1",
    moveLegType: MoveLegType = MoveLegType.PICKUP,
    requestedDate: LocalDate = LocalDate.now().plusDays(1),
    transitDays: Int = 0,
    isCancelLeg: Boolean = false,
    serviceAddress: ServiceAddress? = null,
    containerPlacement: ContainerPlacement? = null,
    zipCode: String = "12345",
    siteId: String = "PME2",
) = UpdateMoveLegRequest(
    orderId = orderId,
    containerOrderId = containerOrderId,
    quoteId = quoteId.toString(),
    moveLegId = moveLegId,
    moveLegType = moveLegType,
    requestedDate = requestedDate,
    transitDays = transitDays,
    isCancelLeg = isCancelLeg,
    serviceAddress = serviceAddress,
    containerPlacement = containerPlacement,
    locationFields = createLocationAvailabilityFields(zipCode, siteId, moveLegType),
)

fun createLocationAvailabilityFields(
    zip: String = "12345",
    siteIdentity: String = "PME2",
    moveLegType: MoveLegType = MoveLegType.PICKUP,
    orderType: OrderType = OrderType.LOCAL,
    isIfOpenCalendar: Boolean = true,
    sessionId: String? = null,
    custTrackingId: String? = null,
): LocationAvailabilityFields = LocationAvailabilityFields(
    zip = zip,
    siteIdentity = siteIdentity,
    moveLegType = moveLegType,
    orderType = orderType,
    isIfOpenCalendar = isIfOpenCalendar,
    sessionId = sessionId,
    custTrackingId = custTrackingId,
)

fun createUpdateMoveLegResponse(
    priceDifference: String? = null,
    quoteId: String? = "0",
) = UpdateMoveLegResponse(
    priceDifference = priceDifference,
    quoteId = quoteId,
)

fun createServiceAddress(
    address1: String = "123 Chicago Ave",
    address2: String = "Unit 1",
    city: String = "Chicago",
    state: String = "IL",
    postalCode: String = "12345",
    country: String = "US",
) = ServiceAddress(
    address1 = address1,
    address2 = address2,
    city = city,
    state = state,
    postalCode = postalCode,
    country = country,
)

fun createSameServiceAreaRequest(
    originalAddress: ServiceAddress = createServiceAddress(),
    updatedAddress: ServiceAddress = createServiceAddress(),
) = SameServiceAreaRequest(
    originalAddress = originalAddress,
    updatedAddress = updatedAddress,
)

fun createOrderInitialDeliveryReviewEntity(
    orderId: String = "orderId",
    customerId: String = "customerId",
    hasReviewed: Boolean = false,
    createdAt: ZonedDateTime = ZonedDateTime.now(),
    updatedAt: ZonedDateTime = ZonedDateTime.now(),
) = OrderInitialDeliveryReviewEntity(
    orderId = orderId,
    customerId = customerId,
    hasReviewed = hasReviewed,
    createdAt = createdAt,
    updatedAt = updatedAt,
)
