package com.pods.mypodsapi.config.filters

import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import com.pods.mypodsapi.customers.Customer
import com.pods.mypodsapi.customers.CustomerType
import com.pods.mypodsapi.fake.customer.createCustomer
import com.pods.mypodsapi.fake.customer.createEmail
import com.pods.mypodsapi.podsready.PodsReadyService.Companion.PODS_READY_COOKIE_NAME
import com.pods.mypodsapi.podsready.PodsReadySessionTokenJwtFactory
import com.pods.mypodsapi.podsready.createPodsReadySessionClaims
import io.mockk.every
import jakarta.servlet.http.Cookie
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import strikt.api.expectThat
import strikt.assertions.isEqualTo

@SpringBootTest
@AutoConfigureMockMvc
class PodsReadySessionFilterTest {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockkBean
    private lateinit var mockPodsReadySessionTokenJwtFactory: PodsReadySessionTokenJwtFactory

    private val expectedTokenClaims = createPodsReadySessionClaims(
        customerId = "customerId",
        email = "email",
        hasPassword = true,
        type = CustomerType.RESIDENTIAL,
        firstName = "firstName",
        lastName = "lastName",
        trackingUuid = "trackingUuid",
    )
    private val expectedCustomer = createCustomer(
        id = expectedTokenClaims.customerId,
        isConverted = expectedTokenClaims.hasPassword,
        firstName = expectedTokenClaims.firstName,
        lastName = expectedTokenClaims.lastName,
        trackingUuid = expectedTokenClaims.trackingUuid,
        email = createEmail(address = expectedTokenClaims.email),
    )
    private val encryptedTokenClaims = "encryptedClaims"

    @BeforeEach
    fun setup() {
        every { mockPodsReadySessionTokenJwtFactory.decodeToken(encryptedTokenClaims) } returns expectedTokenClaims
    }

    @Test
    @Disabled("test fails on ADO but passes locally TODO make work on ADO")
    fun `should pass decrypted access token to pods ready controllers`() {
        val request = MockMvcRequestBuilders
            .get("/v1/legacy/pods-ready/customer")
            .contentType(MediaType.APPLICATION_JSON)
            .cookie(Cookie(PODS_READY_COOKIE_NAME, encryptedTokenClaims))

        val actual = mockMvc.perform(request).andReturn()

        expectThat(actual.response.status).isEqualTo(HttpStatus.OK.value())
        val actualCustomer = objectMapper.readValue(actual.response.contentAsString, Customer::class.java)
        expectThat(actualCustomer).isEqualTo(expectedCustomer)
    }
}
