package com.pods.mypodsapi.containeravailability

import com.pods.mypodsapi.PodsException
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.api.expectThrows
import strikt.assertions.isEqualTo
import java.time.LocalDate

class AvailabilityApiServiceTest {
    private val mockAvailabilityService = mockk<AvailabilityService>()
    private val availabilityApiService = AvailabilityApiService(mockAvailabilityService)

    @Nested
    inner class ContainerAvailability {
        private val customerId = "111222333"
        private val request = createContainerAvailabilityRequest()

        @Test
        fun `returns availability`() {
            val expected = createContainerAvailabilityResponse()
            coEvery { mockAvailabilityService.getContainerAvailability(any(), any()) } returns expected

            val result = availabilityApiService.getContainerAvailability(request, customerId)

            expectThat(result).isEqualTo(expected)
        }

        @Test
        fun `throws an exception when requested date is in the past`() {
            val pastDate = LocalDate.now().minusDays(1)
            val request = createContainerAvailabilityRequest(date = pastDate)

            expectThrows<PodsException> {
                availabilityApiService.getContainerAvailability(request, customerId)
            }
        }
    }
}
