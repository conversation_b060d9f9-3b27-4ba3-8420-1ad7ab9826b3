package com.pods.mypodsapi.containeravailability.legacy

import LegacyAvailabilityService
import com.pods.mypodsapi.containeravailability.createContainerAvailabilityRequest
import com.pods.mypodsapi.external.containeravailability.AvailabilityClient
import com.pods.mypodsapi.external.containeravailability.createPodsContainerAvailabilityResponse
import com.pods.mypodsapi.external.containeravailability.toContainerAvailabilityResponse
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import reactor.core.publisher.Mono
import strikt.api.expectThat
import strikt.assertions.isEqualTo

class LegacyAvailabilityServiceTest {
    private val mockAvailabilityClient = mockk<AvailabilityClient>()
    private val availabilityAdapter =
        LegacyAvailabilityService(mockAvailabilityClient)

    @Nested
    inner class GetContainerAvailability {
        val customerId = "111222333"
        val request = createContainerAvailabilityRequest()

        @Test
        fun `should return locum availability`() =
            runTest {
                val podsResponse = createPodsContainerAvailabilityResponse()
                every { mockAvailabilityClient.getContainerAvailability(any()) } returns Mono.just(podsResponse)

                val actual = availabilityAdapter.getContainerAvailability(request, customerId)

                expectThat(actual).isEqualTo(podsResponse.toContainerAvailabilityResponse())
            }
    }
}
