package com.pods.mypodsapi.orders.usecases

import com.pods.mypodsapi.orders.MoveLegType
import com.pods.mypodsapi.orders.Order
import java.time.LocalDate

class ProcessVisitContainerMoveLegsUseCase {
    fun invoke(orders: List<Order>) {
        orders.forEach { order ->
            order.containers.forEach { container ->
                container.moveLegs.forEachIndexed { index, moveLeg ->
                    if (moveLeg.moveLegType == MoveLegType.VISIT_CONTAINER) {
                        // We need to set this now before we go run logic that sets the scheduled date to the previous
                        val scheduledDate = moveLeg.scheduledDate
                        val today = LocalDate.now()
                        val isContainerVisitScheduled =
                            scheduledDate != null && (scheduledDate.isAfter(today) || scheduledDate == today)
                        if (isContainerVisitScheduled) moveLeg.containerVisitDate = scheduledDate
                    }

                    if (moveLeg.moveLegType == MoveLegType.VISIT_CONTAINER && moveLeg.containerVisitDate == null && index > 0) {
                        val previousLeg = container.moveLegs[index - 1]
                        val newStorageScheduledDate =
                            previousLeg.scheduledDate?.plusDays(previousLeg.transitDays.toLong())
                        moveLeg.scheduledDate = newStorageScheduledDate
                    }
                }
            }
        }
    }
}
