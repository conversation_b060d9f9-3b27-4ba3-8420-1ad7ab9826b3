package com.pods.mypodsapi.orders

import com.pods.mypodsapi.config.filters.ACCESS_TOKEN
import com.pods.mypodsapi.session.AccessTokenClaims
import org.springframework.context.annotation.Profile
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestAttribute
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/order")
@Profile("!fake")
class OrderController(
    private val orderService: OrderApiService,
) {
    @PostMapping("/update-move-leg")
    fun updateMoveLeg(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: UpdateMoveLegRequest,
    ): ResponseEntity<UpdateMoveLegResponse> = ResponseEntity.ok(orderService.updateMoveLeg(claims.customerId, request))

    @PostMapping("/is-same-service-area")
    fun isSameServiceArea(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: SameServiceAreaRequest,
    ): ResponseEntity<Boolean> = ResponseEntity.ok(orderService.isSameServiceArea(request, claims.customerId))

    @PatchMapping("/{orderId}/accept-initial-delivery-placement")
    fun acceptInitialDeliveryPlacement(
        @PathVariable orderId: String,
    ) = orderService.acceptInitialDeliveryPlacement(orderId)
}
