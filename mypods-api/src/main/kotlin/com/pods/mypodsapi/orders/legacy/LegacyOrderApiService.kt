package com.pods.mypodsapi.orders.legacy

import com.pods.mypodsapi.customers.LegacyOrderData
import com.pods.mypodsapi.orders.IOrderService
import com.pods.mypodsapi.orders.Order
import com.pods.mypodsapi.orders.OrderInitialDeliveryReviewService
import com.pods.mypodsapi.orders.SameServiceAreaRequest
import com.pods.mypodsapi.orders.UpdateMoveLegRequest
import com.pods.mypodsapi.orders.UpdateMoveLegResponse
import com.pods.mypodsapi.orders.usecases.FilterOrdersUseCase
import com.pods.mypodsapi.orders.usecases.ProcessContainerMoveLegsForOrders
import com.pods.mypodsapi.orders.usecases.ProcessInitialDeliveryPlacementIsReviewedUseCase
import com.pods.mypodsapi.orders.usecases.ProcessVisitContainerMoveLegsUseCase
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service

@Service
class LegacyOrderApiService(
    private val orderService: IOrderService,
    private val initialDeliveryReviewService: OrderInitialDeliveryReviewService,
) {
    fun getLegacyOrderData(customerId: String): List<LegacyOrderData> =
        runBlocking {
            return@runBlocking orderService.getLegacyOrderData(customerId)
        }

    fun getCustomerOrders(customerId: String): List<Order> =
        runBlocking {
            val orders = orderService.getOrdersByCustomerId(customerId)
            // The order these processors run in matters
            FilterOrdersUseCase().invoke(orders)
            ProcessVisitContainerMoveLegsUseCase().invoke(orders)
            ProcessContainerMoveLegsForOrders().invoke(orders)
            ProcessInitialDeliveryPlacementIsReviewedUseCase().invoke(orders, initialDeliveryReviewService)
            return@runBlocking orders.map { order ->
                run {
                    val newOrder =
                        order.copy(
                            orderId = order.orderId,
                            orderType = order.orderType,
                            quoteId = order.quoteId,
                            orderDate = order.orderDate,
                            price = order.price,
                            containers = order.containers,
                            rentalAgreementSigned = order.rentalAgreementSigned,
                            billingCompanyCode = order.billingCompanyCode,
                        )
                    newOrder.initialDeliveryPlacementIsReviewed = order.initialDeliveryPlacementIsReviewed
                    return@run newOrder
                }
            }
        }

    fun updateMoveLeg(
        customerId: String,
        request: UpdateMoveLegRequest,
    ): UpdateMoveLegResponse =
        runBlocking {
            return@runBlocking orderService.updateMoveLeg(customerId, request)
        }

    fun isSameServiceArea(
        request: SameServiceAreaRequest,
        customerId: String,
    ): Boolean =
        runBlocking {
            return@runBlocking orderService.isSameServiceArea(request, customerId)
        }
}
