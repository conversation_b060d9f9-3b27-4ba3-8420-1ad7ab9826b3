package com.pods.mypodsapi.orders

import com.pods.mypodsapi.PodsException
import com.pods.mypodsapi.document.Document
import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import com.pods.mypodsapi.external.poetFacade.entities.ApplyOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetWareHouseTimingsResponse
import com.pods.mypodsapi.external.poetFacade.mappers.toOrder
import com.pods.mypodsapi.external.poetFacade.mappers.toPoetRequest
import com.pods.mypodsapi.external.poetFacade.toGenerateOrderChangesRequest
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.runBlocking
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.time.LocalDate

const val STALE_DATA_ERROR_MESSAGE = "Data is stale. Please reload the page."

@Service
class OrderService(
    private val poetFacadeClient: PoetFacadeClient,
    private val warehouseHoursService: WarehouseHoursService,
) {
    suspend fun getOrdersByCustomerId(customerId: String): List<Order> {
        val poetOrders =
            poetFacadeClient
                .getOrdersByCustomerId(customerId)
                .awaitFirstOrNull()
                ?: emptyList()
        return poetOrders
            .filter { it.status.isActive }
            .map { it.toOrder() }
            .filter { it.containers.isNotEmpty() }
            .getWarehouseTimings()
    }

    suspend fun List<Order>.getWarehouseTimings(): List<Order> {
        // make warehouseTimings call with all siteIdentities
        val warehousesTimings = warehouseHoursService.getWarehouseTimings()

        // / Update moveLeg eta with the timings based on the day they are scheduled
        updateMovelegsWithETA(warehousesTimings)
        return this
    }

    private fun List<Order>.updateMovelegsWithETA(warehousesTimings: PoetWareHouseTimingsResponse?) {
        warehousesTimings?.takeIf { it.isNotEmpty() }?.let { timings ->
            this.forEach { order ->
                order.containers.forEach { container ->
                    container.moveLegs =
                        container.moveLegs.map { moveLeg ->
                            moveLeg.copy(
                                eta =
                                    calculateEta(
                                        moveLeg.siteIdentity,
                                        moveLeg.scheduledDate,
                                        timings,
                                    ),
                            )
                        }
                }
            }
        }
    }

    private fun calculateEta(
        warehouseId: String,
        scheduledDate: LocalDate?,
        warehousesTimingsResponse: PoetWareHouseTimingsResponse?,
    ): String? {
        if (warehousesTimingsResponse == null || scheduledDate == null) {
            return null
        }

        for (warehouse in warehousesTimingsResponse) {
            val timings = warehouse[warehouseId]?.get(scheduledDate.dayOfWeek)
            if (timings != null && timings.startTime != null && timings.endTime != null) {
                return timings.startTime + " - " + timings.endTime
            }
        }
        return null
    }

    suspend fun getOrderDocumentsForCustomer(customerId: String): List<Document> {
        val poetOrderDocuments =
            poetFacadeClient
                .getOrderDocuments(customerId)
                .awaitFirstOrNull()
                ?: emptyList()
        return poetOrderDocuments.map { it.toDocument() }
    }

    suspend fun updateMoveLeg(request: UpdateMoveLegRequest): UpdateMoveLegResponse {
        val response =
            try {
                poetFacadeClient
                    .generateOrderChanges(request.toGenerateOrderChangesRequest())
                    .awaitFirstOrNull()!!
            } catch (e: WebClientResponseException.BadRequest) {
                throw PodsException.badRequest(
                    internalMessage = e.message.toString(),
                    externalMessage = STALE_DATA_ERROR_MESSAGE,
                )
            }

        if (response.priceDifference != 0L) {
            return response.toOrderChangesResponse()
        }

        val applyToOrderResponse =
            try {
                poetFacadeClient
                    .applyQuoteToOrder(ApplyOrderChangesRequest(response.sfQuoteId))
                    .awaitFirstOrNull()!!
            } catch (e: WebClientResponseException.BadRequest) {
                throw PodsException.badRequest(
                    internalMessage = e.message.toString(),
                    externalMessage = STALE_DATA_ERROR_MESSAGE,
                )
            }

        return applyToOrderResponse.toOrderChangesResponse()
    }

    suspend fun isSameServiceArea(request: SameServiceAreaRequest): Boolean =
        poetFacadeClient
            .getServiceability(request.toPoetRequest())
            .awaitFirstOrNull()!!
            .isServiceable()
}

@Service
class WarehouseHoursService(
    val poetFacadeClient: PoetFacadeClient,
) {
    @Cacheable(value = ["warehouseTimingCache"], key = "'warehouseTimings'")
    fun getWarehouseTimings(): PoetWareHouseTimingsResponse? =
        runBlocking {
            runCatching { getWarehouseTimingsFromPoet() }.getOrNull()
        }

    suspend fun getWarehouseTimingsFromPoet(): PoetWareHouseTimingsResponse? =
        poetFacadeClient
            .getWarehousesTimings()
            .awaitFirstOrNull()
}
