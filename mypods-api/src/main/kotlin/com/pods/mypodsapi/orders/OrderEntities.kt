package com.pods.mypodsapi.orders

import com.fasterxml.jackson.annotation.JsonView
import com.pods.mypodsapi.PodsException
import com.pods.mypodsapi.config.filters.IncludeInLogs
import com.pods.mypodsapi.customers.CustomerType
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrderType
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrderType.INTER_COUNTRY
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrderType.UNKNOWN
import java.time.LocalDate
import java.time.LocalDateTime

data class Order(
    @JsonView(IncludeInLogs::class)
    val orderId: String,
    @JsonView(IncludeInLogs::class)
    val orderType: OrderType?,
    @JsonView(IncludeInLogs::class)
    val quoteId: Long,
    val orderDate: LocalDateTime,
    @JsonView(IncludeInLogs::class)
    val price: Double?,
    @JsonView(IncludeInLogs::class)
    var containers: List<Container>,
    @JsonView(IncludeInLogs::class)
    val rentalAgreementSigned: Boolean = true,
    @JsonView(IncludeInLogs::class)
    val billingCompanyCode: String? = null,
) {
    // default to true so the front end doesn't show the review initial deliver alert
    @JsonView(IncludeInLogs::class)
    var initialDeliveryPlacementIsReviewed: Boolean = true

    @JsonView(IncludeInLogs::class)
    val companyCode = containers.firstOrNull()?.companyCode
}

enum class OrderType(
    @JsonView(IncludeInLogs::class)
    val value: String,
) {
    UNSPECIFIED("UNSPECIFIED"),
    LOCAL("LOCAL"),
    IF("INTERFRANCHISE"),
    IC("INTERCOUNTRY"),
    ANCILLARY_ONLY("ANCILLARYONLY"),
    ;

    companion object {
        fun String?.toOrderType(): OrderType {
            for (leg in OrderType.entries) {
                if (this.equals(leg.value, true)) {
                    return leg
                }
            }
            throw PodsException.internalServerError("Unknown OrderType: $this")
        }

        fun OrderType?.toPoetOrderType(): PoetOrderType =
            when (this) {
                LOCAL -> PoetOrderType.LOCAL
                IF -> PoetOrderType.INTER_FRANCHISE
                IC -> INTER_COUNTRY
                UNSPECIFIED, ANCILLARY_ONLY, null -> UNKNOWN
            }

        fun List<Container>.filterActive(): List<Container> =
            this.filter {
                it.moveLegs.any { moveLeg ->
                    moveLeg.scheduledDate == null ||
                        moveLeg.scheduledDate!! >= LocalDate.now()
                }
            }
    }
}

data class Container(
    @JsonView(IncludeInLogs::class)
    val containerId: String?,
    @JsonView(IncludeInLogs::class)
    val containerOrderId: String? = null,
    @JsonView(IncludeInLogs::class)
    val companyCode: String?,
    @JsonView(IncludeInLogs::class)
    val hasCityServiceMoves: Boolean,
    @JsonView(IncludeInLogs::class)
    var moveLegs: List<MoveLeg>,
    @JsonView(IncludeInLogs::class)
    private val containerType: String?,
    @JsonView(IncludeInLogs::class)
    var upNextMoveLegId: String? = null,
) {
    @JsonView(IncludeInLogs::class)
    val containerSize =
        containerType
            ?.replace("[^0-9]".toRegex(), "")
            ?.let { if (it == "7") "8" else it }
}

// Used to display service countdown label and helper text in the front end
enum class ServiceCountdownType {
    DELIVERY,
    PICKUP,
    DROPOFF,
    MOVE,
    RETURN,
    SELFDELIVERY,
    SELFPICKUP,
    NONE,
}

@JsonView(IncludeInLogs::class)
enum class MoveLegType(
    val value: String,
    val serviceCountdownType: ServiceCountdownType,
    val isSchedulableOnline: Boolean,
    val isTransitLeg: Boolean,
    val displayOnline: Boolean,
    val isPickupType: Boolean,
) {
    INITIAL_DELIVERY("NEW", ServiceCountdownType.DELIVERY, true, false, true, false),
    SELF_INITIAL_DELIVERY("SID", ServiceCountdownType.SELFDELIVERY, false, false, true, false),
    PICKUP("WRT", ServiceCountdownType.PICKUP, true, false, true, true),
    MOVE("MOV", ServiceCountdownType.MOVE, true, false, true, false),
    VISIT_CONTAINER("ACS", ServiceCountdownType.NONE, true, false, true, false),
    REDELIVERY("RDL", ServiceCountdownType.DROPOFF, true, false, true, false),
    WAREHOUSE_TO_WAREHOUSE("WTW", ServiceCountdownType.NONE, false, false, true, false),
    SELF_FINAL_PICKUP("SFP", ServiceCountdownType.SELFPICKUP, true, false, true, true),
    FINAL_PICKUP("FPU", ServiceCountdownType.RETURN, true, false, true, true),
    CITY_SERVICE_DELIVERY("CSDEL", ServiceCountdownType.DELIVERY, false, false, true, false),
    CITY_SERVICE_RETURN("CSRET", ServiceCountdownType.PICKUP, false, false, true, true),
    CITY_SERVICE_REDELIVERY("CSRED", ServiceCountdownType.DROPOFF, false, false, true, false),
    CITY_SERVICE_FINAL_PICKUP("CSFPU", ServiceCountdownType.RETURN, false, false, true, true),
    LOCAL_PORT_TO_LOCAL_PORT("LPLP", ServiceCountdownType.NONE, false, true, true, false),
    LOCAL_PORT_TO_WAREHOUSE("LPLW", ServiceCountdownType.NONE, false, true, true, false),
    WEIGHT_TICKET_EMPTY("PPE", ServiceCountdownType.NONE, false, true, true, false),
    WEIGHT_TICKET_FULL("PPF", ServiceCountdownType.NONE, false, true, true, false),
    PORT_TO_PORT("PTP", ServiceCountdownType.NONE, false, true, true, false),
    PORT_TO_WAREHOUSE("PTW", ServiceCountdownType.NONE, false, true, true, false),
    VISIT_REPOSITION_LEG("VRP", ServiceCountdownType.NONE, false, true, true, false),
    WAREHOUSE_TO_LOCAL_PORT("WTLP", ServiceCountdownType.NONE, false, true, true, false),
    STORAGE_CENTER_TO_PORT("WTP", ServiceCountdownType.NONE, false, true, true, false),
    VISIT_OTHER("VOT", ServiceCountdownType.NONE, false, false, true, false),

    VISIT_LOCKOUT("VLO", ServiceCountdownType.NONE, false, false, false, false),
    VISIT_REMOVE_LOCK("VRL", ServiceCountdownType.NONE, false, false, false, false),
    STORAGE_CENTER_RELOCATION("WRL", ServiceCountdownType.NONE, false, false, false, false),
    UNKNOWN("UNK", ServiceCountdownType.NONE, false, false, false, false),
    ;

    companion object {
        fun String?.toMoveLegType(): MoveLegType {
            for (leg in MoveLegType.entries) {
                if (this.equals(leg.value, true)) {
                    return leg
                }
            }
            return UNKNOWN
        }
    }
}

data class MoveLeg(
    @JsonView(IncludeInLogs::class)
    val moveLegId: String,
    @JsonView(IncludeInLogs::class)
    val moveLegType: MoveLegType,
    @JsonView(IncludeInLogs::class)
    val isCityService: Boolean,
    @JsonView(IncludeInLogs::class)
    val isHawaii: Boolean,
    @JsonView(IncludeInLogs::class)
    val isCrossBorder: Boolean,
    @JsonView(IncludeInLogs::class)
    var scheduledDate: LocalDate?,
    @JsonView(IncludeInLogs::class)
    val eta: String?,
    @JsonView(IncludeInLogs::class)
    val transitDays: Int,
    @JsonView(IncludeInLogs::class)
    val siteIdentity: String,
    @JsonView(IncludeInLogs::class)
    val originationAddress: MoveLegAddress,
    @JsonView(IncludeInLogs::class)
    val destinationAddress: MoveLegAddress,
    // Fields only populated in a container with a list of move legs
    @JsonView(IncludeInLogs::class)
    var containerVisitDate: LocalDate? = null,
    @JsonView(IncludeInLogs::class)
    var isUpNext: Boolean = false,
    @JsonView(IncludeInLogs::class)
    var firstAvailableDate: LocalDate? = null,
    @JsonView(IncludeInLogs::class)
    var lastAvailableDate: LocalDate? = null,
    @JsonView(IncludeInLogs::class)
    var arrivalDate: LocalDate? = null,
    @JsonView(IncludeInLogs::class)
    var moveDate: LocalDate? = null,
) {
    // fields may not be used by the backend but are serialized and returned to the front end
    @JsonView(IncludeInLogs::class)
    val displayAddress: MoveLegAddress =
        if (moveLegType.isPickupType) {
            originationAddress
        } else {
            destinationAddress
        }

    @SuppressWarnings("unused")
    @JsonView(IncludeInLogs::class)
    val serviceCountdownType: ServiceCountdownType = moveLegType.serviceCountdownType

    @JsonView(IncludeInLogs::class)
    var isSchedulableOnline = moveLegType.isSchedulableOnline

    @SuppressWarnings("unused")
    @JsonView(IncludeInLogs::class)
    val isTransitLeg = moveLegType.isTransitLeg
}

data class MoveLegAddress(
    @JsonView(IncludeInLogs::class)
    val address1: String?,
    @JsonView(IncludeInLogs::class)
    val address2: String?,
    @JsonView(IncludeInLogs::class)
    val city: String?,
    @JsonView(IncludeInLogs::class)
    val state: String?,
    @JsonView(IncludeInLogs::class)
    val postalCode: String?,
    @JsonView(IncludeInLogs::class)
    val country: String?,
    @JsonView(IncludeInLogs::class)
    val isStorageCenter: Boolean?,
)

data class UpdateMoveLegRequest(
    @JsonView(IncludeInLogs::class)
    val orderId: String,
    @JsonView(IncludeInLogs::class)
    val containerOrderId: String?,
    @JsonView(IncludeInLogs::class)
    val quoteId: String?, // unless in locum should be int
    @JsonView(IncludeInLogs::class)
    val moveLegId: String,
    @JsonView(IncludeInLogs::class)
    val moveLegType: MoveLegType,
    @JsonView(IncludeInLogs::class)
    val requestedDate: LocalDate?,
    @JsonView(IncludeInLogs::class)
    val transitDays: Int,
    @JsonView(IncludeInLogs::class)
    val isCancelLeg: Boolean,
    @JsonView(IncludeInLogs::class)
    val serviceAddress: ServiceAddress?,
    @JsonView(IncludeInLogs::class)
    val containerPlacement: ContainerPlacement?,
    @JsonView(IncludeInLogs::class)
    val locationFields: LocationAvailabilityFields,

    )

data class LocationAvailabilityFields(
    @JsonView(IncludeInLogs::class)
    val zip: String,
    @JsonView(IncludeInLogs::class)
    val moveLegType: MoveLegType,
    @JsonView(IncludeInLogs::class)
    val orderType: OrderType,
    @JsonView(IncludeInLogs::class)
    val siteIdentity: String,
    @JsonView(IncludeInLogs::class)
    val isIfOpenCalendar: Boolean,
    @JsonView(IncludeInLogs::class)
    val channel: String = "WEB",
    @JsonView(IncludeInLogs::class)
    val customerType: CustomerType? = CustomerType.RESIDENTIAL,
    @JsonView(IncludeInLogs::class)
    val sessionId: String?,
    @JsonView(IncludeInLogs::class)
    val custTrackingId: String?,
)

data class ContainerPlacement(
    @JsonView(IncludeInLogs::class)
    val isPavedSurface: Boolean,
    @JsonView(IncludeInLogs::class)
    val siteType: SiteType = SiteType.DRIVEWAY,
    @JsonView(IncludeInLogs::class)
    val driverNotes: String = "",
    @JsonView(IncludeInLogs::class)
    val placement: PlacementType = PlacementType.DRIVEWAY_STRAIGHT_CLOSE_REAR,
)

enum class PlacementType {
    DRIVEWAY_STRAIGHT_CLOSE_REAR,
    DRIVEWAY_STRAIGHT_CLOSE_CAB,
    DRIVEWAY_STRAIGHT_MIDDLE_REAR,
    DRIVEWAY_STRAIGHT_MIDDLE_CAB,
    DRIVEWAY_STRAIGHT_FAR_REAR,
    DRIVEWAY_STRAIGHT_FAR_CAB,
    DRIVEWAY_CIRCULAR_FAR_CAB,
    DRIVEWAY_CIRCULAR_FAR_REAR,
    DRIVEWAY_CIRCULAR_CLOSE_CAB,
    DRIVEWAY_CIRCULAR_CLOSE_REAR,
    DRIVEWAY_L_SHAPED_CLOSE_REAR,
    DRIVEWAY_L_SHAPED_CLOSE_CAB,
    DRIVEWAY_L_SHAPED_MIDDLE_REAR,
    DRIVEWAY_L_SHAPED_MIDDLE_CAB,
    DRIVEWAY_L_SHAPED_FAR_REAR,
    DRIVEWAY_L_SHAPED_FAR_CAB,
    STREET_LEFT_CAB,
    STREET_FRONT_CAB,
    STREET_RIGHT_CAB,
    STREET_BACK_CAB,
    PARKING_LOT_RIGHT_01,
    PARKING_LOT_RIGHT_02,
    PARKING_LOT_RIGHT_03,
    PARKING_LOT_RIGHT_04,
    PARKING_LOT_FRONT_01,
    PARKING_LOT_FRONT_02,
    PARKING_LOT_FRONT_03,
    PARKING_LOT_FRONT_04,
    PARKING_LOT_BACK_01,
    PARKING_LOT_BACK_02,
    PARKING_LOT_BACK_03,
    PARKING_LOT_BACK_04,
    PARKING_LOT_LEFT_01,
    PARKING_LOT_LEFT_02,
    PARKING_LOT_LEFT_03,
    PARKING_LOT_LEFT_04,
}

enum class SiteType {
    DRIVEWAY,
    STREET,
    PARKING_LOT,
}

@JsonView(IncludeInLogs::class)
data class UpdateMoveLegResponse(
    val quoteId: String?,
    val priceDifference: String?,
)

@JsonView(IncludeInLogs::class)
data class ServiceAddress(
    val address1: String?,
    val address2: String?,
    val city: String,
    val state: String,
    val postalCode: String,
    val country: String,
)

@JsonView(IncludeInLogs::class)
data class SameServiceAreaRequest(
    val originalAddress: ServiceAddress,
    val updatedAddress: ServiceAddress,
)
