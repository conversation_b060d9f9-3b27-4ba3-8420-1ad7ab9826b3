package com.pods.mypodsapi.podsready

import com.pods.mypodsapi.PodsException
import io.jsonwebtoken.Claims
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class PodsReadyEmailTokenJwtFactory(
    @Value("\${pods-ready.email.marketing-cloud-jwt-secret}") private val marketingCloudSigningKey: String,
) {
    private val marketingCloudKey = Keys.hmacShaKeyFor(marketingCloudSigningKey.toByteArray())

    private val jwtParser =
        Jwts
            .parserBuilder()
            .setAllowedClockSkewSeconds(1L)
            .setSigningKey(marketingCloudKey)
            .build()

    fun createToken(request: PodsReadyEmailDebugRequest): String {
        try {
            return Jwts
                .builder()
                .setClaims(request.jwtClaims())
                .signWith(marketingCloudKey)
                .compact()
        } catch (_: Exception) {
            throw PodsException.internalServerError("Unable to produce Pods Email Token JWT.")
        }
    }

    fun decodeToken(token: String): PodsReadyEmailClaims = PodsReadyEmailClaims.from(decodeClaims(token))

    private fun decodeClaims(token: String): Claims = jwtParser.parseClaimsJws(token).body
}
