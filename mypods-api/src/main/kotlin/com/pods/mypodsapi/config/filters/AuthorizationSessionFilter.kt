package com.pods.mypodsapi.config.filters

import com.fasterxml.jackson.databind.ObjectMapper
import com.pods.mypodsapi.config.ErrorResponse
import com.pods.mypodsapi.external.featureFlags.FeatureFlagService
import com.pods.mypodsapi.session.AccessTokenClaims
import com.pods.mypodsapi.session.AuthorizationException
import com.pods.mypodsapi.session.SessionApiService
import com.pods.mypodsapi.session.SessionCookieValues
import com.pods.mypodsapi.session.SessionTokenJwtFactory
import com.pods.mypodsapi.utils.LogAttributes
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import kotlinx.coroutines.runBlocking
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod.OPTIONS
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.security.web.util.matcher.RequestMatcher
import org.springframework.web.filter.OncePerRequestFilter

const val ACCESS_TOKEN = "AccessToken"
val actuatorRequestPath = AntPathRequestMatcher("/actuator/**")
val unprotectedPaths =
    listOf<RequestMatcher>(
        actuatorRequestPath,
        AntPathRequestMatcher("/docs"),
        AntPathRequestMatcher("/v1/session/refresh"),
        AntPathRequestMatcher("/v1/pods-ready/**"),
        AntPathRequestMatcher("/v1/legacy/pods-ready/**"),
        AntPathRequestMatcher("/v1/sample/send-email"),
        AntPathRequestMatcher("/v1/sample/sas-document-url/**"),
        AntPathRequestMatcher("/v1/debug/**"),
    )

class AuthorizationSessionFilter(
    private val requestMatchers: List<RequestMatcher>,
    private val sessionTokenJwtFactory: SessionTokenJwtFactory,
    private val objectMapper: ObjectMapper,
    private val sessionService: SessionApiService,
    private val featureFlagService: FeatureFlagService,
) : OncePerRequestFilter() {
    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain,
    ) = runBlocking {
        val visitorId = LogAttributes.getVisitorId()
        if (!request.isHealthCheck() && featureFlagService.maintenanceModeEnabled(visitorId)) {
            handleFailureToRefresh(response, AuthorizationException.maintenance())
            return@runBlocking
        }

        if (request.isOptionsCall() || request.isUnprotectedPath()) {
            filterChain.doFilter(request, response)
            return@runBlocking
        }

        val sessionCookies = SessionCookieValues.from(request)
        if (sessionCookies.accessTokenCookie == null) {
            logger.error("No access token cookie present")
            tryRefreshSession(request, response, filterChain)
            return@runBlocking
        }

        try {
            val claims = sessionTokenJwtFactory.decodeAccessToken(sessionCookies.accessTokenCookie.value)
            logAccessTokenClaims(claims)
            request.setAttribute(ACCESS_TOKEN, claims)
            filterChain.doFilter(request, response)
        } catch (e: Exception) {
            logger.error("Failed to decrypt access token", e)
            tryRefreshSession(request, response, filterChain)
        }
    }

    private fun tryRefreshSession(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain,
    ) {
        fun handleSuccessfulRefresh(
            claims: AccessTokenClaims,
            refreshedSession: HttpHeaders,
        ) {
            request.setAttribute(ACCESS_TOKEN, claims)
            val setCookies = refreshedSession[HttpHeaders.SET_COOKIE]
            setCookies?.forEach { response.addHeader(HttpHeaders.SET_COOKIE, it) }
            logAccessTokenClaims(claims)
            filterChain.doFilter(request, response)
        }

        val sessionCookies = SessionCookieValues.from(request)
        try {
            val refreshedSession = sessionService.getRefreshedSession(sessionCookies)
            // Valid session has access token
            val claims = sessionTokenJwtFactory.decodeAccessToken(refreshedSession.newSession.refreshedAccessToken!!)
            handleSuccessfulRefresh(claims, refreshedSession.newSession.httpHeaders)
        } catch (exception: AuthorizationException) {
            logger.error("Failed to refresh session $exception", exception)
            handleFailureToRefresh(response, exception)
        } catch (exception: Exception) {
            logger.error("Failed to refresh session $exception", exception)
            handleFailureToRefresh(response)
        }
    }

    private fun logAccessTokenClaims(claims: AccessTokenClaims) {
        logger.info("CustomerID: '${claims.customerId}'")
        logger.info("Access Token Claims: $claims")
        LogAttributes.setCustomerId(claims.customerId)
    }

    private fun handleFailureToRefresh(
        response: HttpServletResponse,
        exception: AuthorizationException = AuthorizationException.unauthorized(),
    ) {
        val errorResponse = ErrorResponse("Failed authorization", exception.status.name)
        val errorResponseJson = objectMapper.writeValueAsString(errorResponse)
        response.contentType = "application/json"
        response.status = exception.httpStatus.value()
        response.writer.write(errorResponseJson)
        response.writer.flush()
    }

    private fun HttpServletRequest.isUnprotectedPath() = requestMatchers.any { it.matches(this) }

    private fun HttpServletRequest.isHealthCheck() = actuatorRequestPath.matches(this)
}

fun HttpServletRequest.isOptionsCall() = method == OPTIONS.toString()
