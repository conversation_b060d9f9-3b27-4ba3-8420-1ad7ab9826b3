package com.pods.mypodsapi.debug.usecases

import com.pods.mypodsapi.document.pdfUseCases.LanternFlyMothCsv
import org.springframework.stereotype.Component

data class MothInspectionCsvRow(
    val id: String,
    val category: String,
    val subcategory: String,
    val checkboxLabel: String,
    val cdfaCheckboxName: String,
    val usdaCategory: String,
    val restricted: Boolean,
) {
    companion object {
        fun fromCsvRow(row: Map<String, String?>): MothInspectionCsvRow =
            MothInspectionCsvRow(
                id = row["Id"]!!,
                category = row["WebCategory"]!!,
                subcategory = row["WebSubCategory"]!!,
                checkboxLabel = row["WebCheckboxLabel"]!!,
                cdfaCheckboxName = row["CdfaPdfCheckboxName"]!!,
                usdaCategory = row["UsdaPdfCategory"]!!,
                restricted = row["Restricted"]!!.toBoolean(),
            )
    }
}

data class MothCategory(
    val titleKey: String,
    val subcategories: MutableList<MothSubcategory>,
)

data class MothSubcategory(
    val subtitleKey: String,
    val checkboxes: MutableList<MothCheckbox>,
)

data class MothCheckbox(
    val id: String,
    val label: String,
    val pdfKey: String,
    val checked: Boolean,
    val restricted: Boolean,
)

typealias MothCategoryLabel = String
typealias MothSubcategoryLabel = String
typealias MothCheckboxId = String

data class MothOtherCheckbox(
    val id: MothCheckboxId,
    val description: String,
)

@Component
class GetMothInspectionConfig(
    private val lanternFlyMothCsv: LanternFlyMothCsv,
) {
    fun invoke(): List<MothCategory> {
        val rows = lanternFlyMothCsv.rows

        val resultMap =
            mutableMapOf<MothCategoryLabel, MutableMap<MothSubcategoryLabel, MutableMap<MothCheckboxId, MothInspectionCsvRow>>>()
        val categories = rows.groupBy { it.category }
        categories.forEach { category ->
            if (!resultMap.containsKey(category.key)) {
                resultMap[category.key] = mutableMapOf()
            }

            val categoryEntry = resultMap[category.key]!!
            val subcategories = category.value.groupBy { it.subcategory }
            subcategories.forEach { subcategory ->
                if (!categoryEntry.containsKey(subcategory.key)) {
                    categoryEntry[subcategory.key] = mutableMapOf()
                }

                val subcategoryEntry = categoryEntry[subcategory.key]!!
                val checkboxes = subcategory.value
                checkboxes.forEach { checkbox ->
                    subcategoryEntry[checkbox.id] = checkbox
                }
            }
        }

        val result =
            resultMap.map { category ->
                MothCategory(
                    titleKey = category.key,
                    subcategories =
                        category.value
                            .map { subcategory ->
                                MothSubcategory(
                                    subtitleKey = subcategory.key,
                                    checkboxes =
                                        subcategory.value
                                            .map { checkbox ->
                                                MothCheckbox(
                                                    id = checkbox.value.id,
                                                    label = checkbox.value.checkboxLabel,
                                                    pdfKey = checkbox.value.cdfaCheckboxName,
                                                    checked = false,
                                                    restricted = checkbox.value.restricted,
                                                )
                                            }.toMutableList(),
                                )
                            }.toMutableList(),
                )
            }
        return result
    }
}
