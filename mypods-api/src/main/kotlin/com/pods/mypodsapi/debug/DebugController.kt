package com.pods.mypodsapi.debug

import com.pods.mypodsapi.customers.CustomerAddress
import com.pods.mypodsapi.debug.usecases.GetMothInspectionConfig
import com.pods.mypodsapi.debug.usecases.MothCategory
import com.pods.mypodsapi.document.SignFnpsRequest
import com.pods.mypodsapi.document.SignMothAgreementRequest
import com.pods.mypodsapi.document.SignMothAgreementRequest.Companion.toUsdaRequest
import com.pods.mypodsapi.document.pdfHeader
import com.pods.mypodsapi.document.pdfUseCases.FillInCdfaPdfRequest
import com.pods.mypodsapi.document.pdfUseCases.FillInCdfaPdfUseCase
import com.pods.mypodsapi.document.pdfUseCases.FillInFnpsPdfUseCase
import com.pods.mypodsapi.document.pdfUseCases.FillInUsdaPdfUseCase
import com.pods.mypodsapi.document.pdfUseCases.LanternFlyMothCsv
import com.pods.mypodsapi.orders.OrderInitialDeliveryReviewEntity
import com.pods.mypodsapi.orders.OrderInitialDeliveryReviewRepository
import com.pods.mypodsapi.podsready.PodsReadyEmailDebugRequest
import com.pods.mypodsapi.podsready.PodsReadyEmailTokenJwtFactory
import com.pods.mypodsapi.session.AccessTokenClaims
import com.pods.mypodsapi.session.AuthorizationException
import com.pods.mypodsapi.session.SessionTokenJwtFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.CacheManager
import org.springframework.cache.caffeine.CaffeineCache
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime

@SuppressWarnings("LongParameterList", "TooManyFunctions")
@RestController
@RequestMapping("/v1/debug")
class DebugController(
    private val sessionTokenJwtFactory: SessionTokenJwtFactory,
    private val podsReadyEmailTokenJwtFactory: PodsReadyEmailTokenJwtFactory,
    @Value("\${spring.profiles.active}")
    private val activeProfile: String,
    private val fillInFnpsPdfUseCase: FillInFnpsPdfUseCase,
    private val fillInCdfaPdf: FillInCdfaPdfUseCase,
    private val fillInUsdaPdf: FillInUsdaPdfUseCase,
    private val getMothInspectionConfig: GetMothInspectionConfig,
    private val lanternFlyMothCsv: LanternFlyMothCsv,
    private val initialDeliverReviewRepository: OrderInitialDeliveryReviewRepository,
    private val cacheManager: CacheManager,
) {
    private fun throwIfProd() {
        if (activeProfile.contains("prod", true)) {
            throw AuthorizationException.forbidden()
        }
    }

    @PostMapping("/pods-ready-token")
    fun generatePodsReadyToken(
        @RequestBody request: PodsReadyEmailDebugRequest
    ): ResponseEntity<String> {
        throwIfProd()
        val response = podsReadyEmailTokenJwtFactory.createToken(request)
        return ResponseEntity.ok(response)
    }

    @PostMapping("/access-token")
    fun getCustomerOrders(
        @RequestBody claims: AccessTokenClaims,
    ): ResponseEntity<String> {
        throwIfProd()
        val accessToken = sessionTokenJwtFactory.createAccessToken(claims)
        return ResponseEntity.ok(accessToken)
    }

    @GetMapping("/timeout")
    fun timeout(
        @RequestParam
        timeout: Long,
    ): ResponseEntity<String> {
        throwIfProd()
        Thread.sleep(timeout)
        return ResponseEntity.ok("Success")
    }

    @GetMapping("/fnps-pdf")
    fun getFnpsPdf(): ResponseEntity<ByteArray> {
        throwIfProd()
        val customerId = "*********"
        val orderId = "12453"
        val moveId = "12345"
        val request =
            SignFnpsRequest(
                firstName = "John",
                lastName = "Smith",
                orderId = orderId,
                moveId = moveId,
                address =
                    CustomerAddress(
                        id = 1,
                        addressType = "Mailing",
                        address1 = "1233 Lagoon Rd Apt A",
                        address2 = null,
                        city = "Tarpon Springs",
                        state = "FL",
                        postalCode = "12345",
                        regionCode = "US",
                    ),
                timezoneOffset = 0,
            )

        val updatedPdf =
            fillInFnpsPdfUseCase.invoke(
                request,
                customerId,
            )
        return ResponseEntity(updatedPdf, pdfHeader(), HttpStatus.OK)
    }

    @GetMapping("/cdfa-pdf")
    fun getCdfaPdf(): ResponseEntity<ByteArray> {
        throwIfProd()
        val csvRows = lanternFlyMothCsv.rows
        val allCheckboxes = csvRows.map { row -> row.id }
        val request =
            FillInCdfaPdfRequest(
                "John",
                "Doe",
                "15 Pumpkin Pl., Durham, NC 27730",
                "<EMAIL>",
                allCheckboxes,
                emptyList(),
                dateSigned = ZonedDateTime.now(),
            )

        val updatedPdf = fillInCdfaPdf.invoke(request)
        return ResponseEntity(updatedPdf, pdfHeader(), HttpStatus.OK)
    }

    @PostMapping("/usda-pdf")
    fun getUsdaPdf(
        @RequestParam customerId: String,
        @RequestBody request: SignMothAgreementRequest,
    ): ResponseEntity<ByteArray> {
        throwIfProd()
        val fillInUsdaPdfRequest = request.toUsdaRequest(customerId)
        val updatedPdf = fillInUsdaPdf.invoke(fillInUsdaPdfRequest)
        return ResponseEntity(updatedPdf, pdfHeader(), HttpStatus.OK)
    }

    @GetMapping("/moth-inspection-config")
    fun getMothInspectionConfig(): ResponseEntity<List<MothCategory>> {
        throwIfProd()
        val result = getMothInspectionConfig.invoke()
        return ResponseEntity.ok().body(result)
    }

    @GetMapping("/initial-deliveries-to-review")
    fun getInitialDeliveriesToReview(): ResponseEntity<List<OrderInitialDeliveryReviewEntity>> {
        throwIfProd()
        // Should not call repositories directly, should go through a service, but this is temporary
        val result = initialDeliverReviewRepository.findAll().toList()
        return ResponseEntity.ok(result)
    }

    @GetMapping("/inspect-cache")
    fun inspectCache(): ResponseEntity<String> {
        inspectCaffeineCache()
        return ResponseEntity.ok("Cache inspected. Check logs for details.")
    }

    fun inspectCaffeineCache() {
        val cacheNames = cacheManager.cacheNames
        val caches = cacheNames.map { cacheManager.getCache(it) as? CaffeineCache }
        caches.forEach {
            if (it != null) {
                println("***CACHE - ${it.name} ***")
                val nativeCache = it.nativeCache as com.github.benmanes.caffeine.cache.Cache<*, *>
                println("count of keys-> " + nativeCache.estimatedSize())
                nativeCache.asMap().forEach { (key, value) ->
                    println("Key: $key, Value: $value")
                }
            } else {
                println("Cache not found or not a CaffeineCache.")
            }
        }
    }
}
