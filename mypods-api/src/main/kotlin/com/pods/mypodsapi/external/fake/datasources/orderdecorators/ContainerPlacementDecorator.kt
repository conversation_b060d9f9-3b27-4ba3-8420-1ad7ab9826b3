package com.pods.mypodsapi.external.fake.datasources.orderdecorators

import com.pods.mypodsapi.external.fake.datasources.IDataSource
import com.pods.mypodsapi.external.fake.datasources.modifyMoveLeg
import com.pods.mypodsapi.external.fake.datasources.withContainerId
import com.pods.mypodsapi.external.fake.datasources.withContainers
import com.pods.mypodsapi.external.locum.entities.LocumCustomerOrdersResponse
import java.time.LocalDateTime

class ContainerPlacementDecorator(
    private val source: IDataSource<LocumCustomerOrdersResponse>,
) : IDataSource<LocumCustomerOrdersResponse> {
    override fun getData(): LocumCustomerOrdersResponse {
        val order = source.getData()
        val firstOrder = order.response!!.first()
        val today = LocalDateTime.now()
        val future = today.plusWeeks(1)
        val baseContainer =
            firstOrder.containers
                .first()
                .modifyMoveLeg(0) { it.copy(scheduledDate = today) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = null) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = null) }

        val containers =
            listOf(
                baseContainer
                    .modifyMoveLeg(1) { it.copy(scheduledDate = future) }
                    .withContainerId("scheduled future"),
                baseContainer
                    .modifyMoveLeg(1) { it.copy(scheduledDate = null) }
                    .withContainerId("not scheduled"),
            )
        return order.withContainers(containers)
    }
}
