package com.pods.mypodsapi.external.fake.datasources.orderdecorators

import com.pods.mypodsapi.external.fake.datasources.IDataSource
import com.pods.mypodsapi.external.fake.datasources.modifyMoveLeg
import com.pods.mypodsapi.external.fake.datasources.withContainerId
import com.pods.mypodsapi.external.fake.datasources.withContainers
import com.pods.mypodsapi.external.locum.entities.LocumCustomerOrdersResponse
import java.time.LocalDateTime

class IFSelfDeliveryDecorator(private val source: IDataSource<LocumCustomerOrdersResponse>) :
    IDataSource<LocumCustomerOrdersResponse> {
    override fun getData(): LocumCustomerOrdersResponse {
        val order = source.getData()
        val firstOrder = order.response!!.first()
        val today = LocalDateTime.now()
        val future = today.plusDays(7)
        val past = today.minusDays(7)
        val baseContainer = firstOrder.containers.first()
            .modifyMoveLeg(0) { it.copy(scheduledDate = today) }
            .modifyMoveLeg(1) { it.copy(scheduledDate = null) }
            .modifyMoveLeg(2) { it.copy(scheduledDate = null) }
            .modifyMoveLeg(3) { it.copy(scheduledDate = null) }
        val containers = listOf(
            baseContainer.withContainerId("self initial delivery today"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = future) }
                .withContainerId("self initial delivery future"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .withContainerId("self initial delivery past"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = today) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = future) }
                .withContainerId("Storage center to storage center future"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = today) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = future) }
                .withContainerId("Redelivery future"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = today) }
                .modifyMoveLeg(3) { it.copy(scheduledDate = future) }
                .withContainerId("Final pickup future"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(3) { it.copy(scheduledDate = today) }
                .withContainerId("final pickup today"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = future) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = future) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = future) }
                .modifyMoveLeg(3) { it.copy(scheduledDate = future) }
                .withContainerId("all scheduled future"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(3) { it.copy(scheduledDate = today) }
                .withContainerId("most scheduled past"),
        )
        return order.withContainers(containers)
    }

}
