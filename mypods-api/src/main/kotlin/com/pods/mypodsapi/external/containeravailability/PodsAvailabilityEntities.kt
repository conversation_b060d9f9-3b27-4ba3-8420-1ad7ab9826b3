package com.pods.mypodsapi.external.containeravailability

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.annotation.JsonView
import com.pods.mypodsapi.config.filters.IncludeInLogs
import java.time.LocalDate

data class PodsContainerAvailabilityResponse(
    @JsonProperty("CallInfo")
    val callInfo: PodsCallInfo,
    @JsonProperty("Location")
    val location: PodsLocation,
    @JsonProperty("GeneralAvailabilityDates")
    val generalAvailabilityDates: List<PodsAvailabilityDate>,
    @JsonProperty("SevenFootAvailabilityDates")
    val sevenFootAvailabilityDates: List<PodsAvailabilityDate>,
    @JsonProperty("TwelveFootAvailabilityDates")
    val twelveFootAvailabilityDates: List<PodsAvailabilityDate>,
    @JsonProperty("SixteenFootAvailabilityDates")
    val sixteenFootAvailabilityDates: List<PodsAvailabilityDate>,
)

data class PodsCallInfo(
    @JsonProperty("ExceptionDetails")
    val exceptionDetails: Any?,
    @JsonProperty("ServerUsed")
    val serverUsed: String?,
    @JsonProperty("StatusCode")
    val statusCode: PodsServiceabilityStatusCode?,
    @JsonProperty("StatusMessage")
    val statusMessage: String?,
)

data class PodsLocation(
    @JsonProperty("AddressLine1")
    val addressLine1: Any?,
    @JsonProperty("AddressLine2")
    val addressLine2: Any?,
    @JsonProperty("City")
    val city: String?,
    @JsonProperty("ID")
    val id: String?,
    @JsonProperty("Name")
    val name: Any?,
    @JsonProperty("PostalCode")
    val postalCode: Any?,
    @JsonProperty("RegionCode")
    val regionCode: String?,
    @JsonProperty("StateProvince")
    val stateProvince: String?,
    @JsonProperty("Warehouses")
    val warehouses: List<PodsWarehouse>?,
)

data class PodsWarehouse(
    @JsonProperty("AddressForQuote")
    val addressForQuote: String?,
    @JsonProperty("AddressLine1")
    val addressLine1: String?,
    @JsonProperty("AddressLine2")
    val addressLine2: String?,
    @JsonProperty("City")
    val city: String?,
    @JsonProperty("ID")
    val id: String?,
    @JsonProperty("Latitude")
    val latitude: Double?,
    @JsonProperty("Longitude")
    val longitude: Double?,
    @JsonProperty("Name")
    val name: String?,
    @JsonProperty("OperationSchedule")
    val operationSchedule: Any?,
    @JsonProperty("PhoneList")
    val phoneList: Any?,
    @JsonProperty("PostalCode")
    val postalCode: String?,
    @JsonProperty("RegionCode")
    val regionCode: String?,
    @JsonProperty("StateProvince")
    val stateProvince: String?,
)

enum class PodsServiceabilityStatusCode(
    @JsonValue val str: String,
) {
    OK("OK"),
    BAD_ZIP("BadZip"),
    OUT_OF_MARKET("OutOfMarket"),
    OUT_OF_MARKET_ORIGIN("OutOfMarket-Origin"),
    OUT_OF_MARKET_ORIGINATION("OutOfMarket-Origination"),
    OUT_OF_MARKET_DESTINATION("OutOfMarket-Destination"),
    OUT_OF_MARKET_BOTH("OutOfMarket-Both"),
    EXCEPTION("Exception"),

    // added these to support locum container availability endpoint
    POSTAL_CODE_EXCEPTION("PostalCodeException"),
    CITY_SERVICE("CityServiceQualifier"),
    IF_CITY_SERVICE("IFCityServiceQualifier"),
    HAWAII_MARKET("HawaiiMarket"),
    IFNBF("IFNBF"),
    IFLP("IFLP"),
}

data class PodsAvailabilityDate(
    @JsonProperty("DateTime")
    val dateTime: LocalDate,
    @JsonProperty("IsAvailable")
    val isAvailable: Boolean,
    @JsonProperty("LocalSalesIndicator")
    val localSalesIndicator: Boolean,
)

@JsonView(IncludeInLogs::class)
data class PodsContainerAvailabilityRequest(
    @JsonProperty("Zip")
    val zip: String,
    @JsonProperty("ServiceType")
    val serviceType: String,
    @JsonProperty("OrderType")
    val orderType: String,
    @JsonProperty("ReferenceDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    val referenceDate: LocalDate?,
    @JsonProperty("Channel")
    val channel: String = "WEB",
)
