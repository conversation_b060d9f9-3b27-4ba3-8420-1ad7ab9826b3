package com.pods.mypodsapi.external.fake

import com.pods.mypodsapi.customers.UpdateAccountRequest
import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import com.pods.mypodsapi.external.poetFacade.entities.AddWarehouseVisitRequest
import com.pods.mypodsapi.external.poetFacade.entities.ApplyOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.ApplyQuoteToOrderResponse
import com.pods.mypodsapi.external.poetFacade.entities.GenerateOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.GenerateQuoteForOrderChangesResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetAddPaymentMethodRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetContainerAvailabilityRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetContainerAvailabilityResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetCustomStatementRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetCustomer
import com.pods.mypodsapi.external.poetFacade.entities.PoetDocument
import com.pods.mypodsapi.external.poetFacade.entities.PoetInvoice
import com.pods.mypodsapi.external.poetFacade.entities.PoetInvoicePaymentResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetMakePaymentRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrder
import com.pods.mypodsapi.external.poetFacade.entities.PoetPaymentMethodResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetServiceabilityRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetServiceabilityResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetSetDefaultPaymentRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetUploadDocumentRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetWareHouseTimingsResponse
import com.pods.mypodsapi.external.poetFacade.entities.RemoveWarehouseVisitRequest
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono
import java.time.LocalDate

@Component
@Profile("fake")
class FakePoetFacadeClient(
    private val readFromResource: ReadFromResource,
) : PoetFacadeClient {
    override fun getCustomer(customerId: String): Mono<PoetCustomer> {
        val fileName = "/clientResponses/poetFacade/customer/poetCustomer.json"
        val customer = readFromResource<PoetCustomer>(fileName)
        return Mono.just(customer)
    }

    override fun getOrdersByCustomerId(customerId: String): Mono<List<PoetOrder>> {
        val fileName = "/clientResponses/poetFacade/orders/poetCustomerOrders.json"
        val orders = readFromResource<List<PoetOrder>>(fileName)
        return Mono.just(orders)
    }

    override fun getCustomStatement(request: PoetCustomStatementRequest): Mono<ByteArray> = Mono.just(ByteArray(1))

    override fun updateAccount(
        customerId: String,
        body: UpdateAccountRequest,
    ): Mono<Unit> = Mono.empty()

    override fun generateOrderChanges(body: GenerateOrderChangesRequest): Mono<GenerateQuoteForOrderChangesResponse> =
        Mono.just(GenerateQuoteForOrderChangesResponse("*********", 0L))

    override fun applyQuoteToOrder(body: ApplyOrderChangesRequest): Mono<ApplyQuoteToOrderResponse> =
        Mono.just(ApplyQuoteToOrderResponse("*********"))

    override fun getCustomerDocuments(customerId: String): Mono<List<PoetDocument>> {
        val fileName = "/clientResponses/poetFacade/documents/customerDocuments.json"
        val orders = readFromResource<List<PoetDocument>>(fileName)
        return Mono.just(orders)
    }

    override fun getOrderDocuments(customerId: String): Mono<List<PoetDocument>> {
        val fileName = "/clientResponses/poetFacade/documents/orderDocuments.json"
        val orders = readFromResource<List<PoetDocument>>(fileName)
        return Mono.just(orders)
    }

    override fun getCustomerInvoices(customerId: String): Mono<List<PoetInvoice>> {
        val fileName = "/clientResponses/poetFacade/documents/customerInvoices.json"
        val orders = readFromResource<List<PoetInvoice>>(fileName)
        return Mono.just(orders)
    }

    override fun uploadDocument(request: PoetUploadDocumentRequest): Mono<Unit> = Mono.just(Unit)

    override fun getPaymentMethods(customerId: String): Mono<PoetPaymentMethodResponse> {
        val fileName = "/clientResponses/poetFacade/payments/paymentMethods.json"
        val response = readFromResource<PoetPaymentMethodResponse>(fileName)
        return Mono.just(response)
    }

    override fun addPaymentMethod(request: PoetAddPaymentMethodRequest): Mono<Unit> = Mono.just(Unit)

    override fun makePayments(request: PoetMakePaymentRequest): Mono<PoetInvoicePaymentResponse> =
        Mono.just(
            PoetInvoicePaymentResponse(emptyList()),
        )

    override fun setDefaultPaymentMethod(request: PoetSetDefaultPaymentRequest): Mono<Unit> = Mono.just(Unit)

    override fun getContainerAvailability(request: PoetContainerAvailabilityRequest): Mono<PoetContainerAvailabilityResponse> {
        val fileName = "/clientResponses/poetFacade/location/containerAvailability.json"
        val response = readFromResource<PoetContainerAvailabilityResponse>(fileName)
        return Mono.just(responseWithAvailabilityForDate(response, request.referenceDate!!))
    }

    override fun getServiceability(request: PoetServiceabilityRequest): Mono<PoetServiceabilityResponse> {
        val fileName = "/clientResponses/poetFacade/location/serviceabilityResponse.json"
        val response = readFromResource<PoetServiceabilityResponse>(fileName)
        return Mono.just(response)
    }

    override fun getWarehousesTimings(): Mono<PoetWareHouseTimingsResponse> {
        val fileName = "/clientResponses/poetFacade/location/warehouseTimingsResponse.json"
        val response = readFromResource<PoetWareHouseTimingsResponse>(fileName)
        return Mono.just(response)
    }

    override fun addWarehouseVisit(request: AddWarehouseVisitRequest): Mono<GenerateQuoteForOrderChangesResponse> =
        Mono.just(GenerateQuoteForOrderChangesResponse(request.orderId, 0L))

    override fun cancelWarehouseVisit(request: RemoveWarehouseVisitRequest): Mono<GenerateQuoteForOrderChangesResponse> =
        Mono.just(GenerateQuoteForOrderChangesResponse(request.orderId, 0L))

    // create a list of availability for 90days from the date from the requested date
    // And set availability to false for every 4th entry
    private fun responseWithAvailabilityForDate(
        response: PoetContainerAvailabilityResponse,
        referenceDate: LocalDate,
    ): PoetContainerAvailabilityResponse {
        val dynamicAvailablity =
            response.eightFtContainerAvailability.mapIndexed { index, availability ->
                availability.copy(date = referenceDate.plusDays(index.toLong()), isAvailable = index % 7 != 6)
            }
        return response.copy(
            eightFtContainerAvailability = dynamicAvailablity,
            twelveFtContainerAvailability = dynamicAvailablity,
            sixteenFtContainerAvailability = dynamicAvailablity,
        )
    }
}
