package com.pods.mypodsapi.external.poetFacade.cacheservice

import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import com.pods.mypodsapi.external.poetFacade.entities.PoetDocument
import com.pods.mypodsapi.external.poetFacade.entities.PoetOrder
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.runBlocking
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Component

@Component
class PoetFacadeCacheService(
    private val poetFacadeClient: PoetFacadeClient,
    private val cacheManager: CacheManager,
) {
    @Cacheable(value = ["customerDocumentsCache"])
    fun getCustomerDocumentsFromPoet(customerId: String): List<PoetDocument> =
        runBlocking {
            poetFacadeClient
                .getCustomerDocuments(customerId)
                .awaitFirstOrNull()
                ?: emptyList()
        }

    @Cacheable(value = ["orderDocumentsCache"])
    fun getOrderDocumentsFromPoet(customerId: String): List<PoetDocument> =
        runBlocking {
            poetFacadeClient
                .getOrderDocuments(customerId)
                .awaitFirstOrNull()
                ?: emptyList()
        }

    fun clearDocumentsCaches(customerId: String) {
        cacheManager.getCache("customerDocumentsCache")?.evictIfPresent(customerId)
        cacheManager.getCache("orderDocumentsCache")?.evictIfPresent(customerId)
    }

    @Cacheable(value = ["ordersCache"])
    fun getOrdersFromPoet(customerId: String): List<PoetOrder> =
        runBlocking { poetFacadeClient.getOrdersByCustomerId(customerId).awaitFirstOrNull() ?: emptyList() }

    fun clearOrdersCache(customerId: String) {
        cacheManager.getCache("ordersCache")?.evictIfPresent(customerId)
    }
}
