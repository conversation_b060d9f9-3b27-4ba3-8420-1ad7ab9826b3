package com.pods.mypodsapi.external.poetFacade.entities

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonView
import com.pods.mypodsapi.config.filters.IncludeInLogs
import com.pods.mypodsapi.customers.CustomerType
import com.pods.mypodsapi.customers.exceptions.PayInvoiceCreditCardException
import com.pods.mypodsapi.document.Document
import com.pods.mypodsapi.document.DocumentType
import com.pods.mypodsapi.document.Invoice
import com.pods.mypodsapi.orders.ContainerPlacement
import com.pods.mypodsapi.orders.MoveLegType
import com.pods.mypodsapi.orders.OrderType
import com.pods.mypodsapi.orders.UpdateMoveLegResponse
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

@JsonIgnoreProperties(ignoreUnknown = true)
data class PoetCustomer(
    val cuid: String,
    val firstName: String,
    val lastName: String,
    val email: String,
    val smsOptIn: Boolean,
    val customerType: CustomerType,
    val billingAddress: PoetAccountAddress?,
    val shippingAddress: PoetAccountAddress?,
    val primaryPhone: PoetAccountPhone?,
    val secondaryPhone: PoetAccountPhone?,
    val militaryBranch: String,
    val militaryStatus: String,
)

data class PoetAccountAddress(
    val city: String,
    val countryCode: String,
    val isPrimary: Boolean,
    val zipCode: String,
    val state: String,
    val street: String,
)

data class PoetAccountPhone(
    val phone: String,
)

data class PoetOrder(
    val orderId: String,
    val quoteId: Long,
    val orderType: PoetOrderType?,
    val orderDate: LocalDateTime,
    val rentalAgreementSigned: Boolean,
    val totalOrderPriceCents: Long?,
    val containers: List<PoetContainer>?,
    val status: OrderStatus,
    val billingCompanyCode: String?,
)

enum class ContainerStatus {
    CANCELLED,
    BOOKED,
    INVOICED,
    UNKNOWN,
    ;

    val isActive: Boolean
        get() = this != CANCELLED
}

data class PoetContainer(
    val containerId: String?,
    val containerOrderId: String,
    val orderOriginCompanyCode: String?,
    val containerSize: PoetContainerSize,
    val hasCityServiceMoveLeg: Boolean,
    val moveLegs: List<PoetMoveLeg>,
    val status: ContainerStatus,
)

data class PoetMoveLeg(
    val moveLegId: String,
    val moveLegType: MoveLegType,
    val transitDays: Int,
    val scheduledDate: LocalDate?,
    val status: OrderStatus,
    val isCityService: Boolean,
    val isHawaii: Boolean,
    val isCrossBorder: Boolean,
    val dayOfEtaWindow: String?,
    val originationAddress: PoetMoveLegAddress,
    val destinationAddress: PoetMoveLegAddress,
    val siteIdentity: String,
    val moveNumber: String,
)

data class PoetMoveLegAddress(
    val address1: String?,
    val address2: String?,
    val city: String?,
    val state: String?,
    val postalCode: String?,
    val country: String?,
    val isStorageCenter: Boolean?,
)

// TODO swap our domain to use the enum instead of looking at the number in the string
enum class PoetContainerSize(
    val containerSize: String,
) {
    CONTAINER_SIZE_16("16"),
    CONTAINER_SIZE_12("12"),
    CONTAINER_SIZE_8("8"),
}

data class PoetCustomStatementRequest(
    val customerId: String,
    val startDate: LocalDate,
    val endDate: LocalDate,
)

enum class PoetOrderType {
    LOCAL,
    INTER_FRANCHISE,
    INTER_COUNTRY,
    UNKNOWN,
    ;

    companion object {
        fun PoetOrderType.toOrderType(): OrderType =
            when (this) {
                LOCAL -> OrderType.LOCAL
                INTER_FRANCHISE -> OrderType.IF
                INTER_COUNTRY -> OrderType.IC
                UNKNOWN -> OrderType.UNSPECIFIED
            }
    }
}

enum class OrderStatus {
    CANCELLED,
    BOOKED,
    INVOICED,
    UNKNOWN,
    ;

    val isActive: Boolean
        get() = this != CANCELLED
}

data class PoetDocument(
    val docRef: String,
    val docName: String,
    val isCustomerFacing: Boolean,
    val id: String,
    val docType: PoetDocumentType,
    val orderId: String?,
    val tags: String?,
    val docNotes: String,
    val invoiceNumber: String?,
) {
    fun toDocument(): Document =
        Document(
            orderId = orderId ?: "",
            id = id,
            type = docType.toString(),
            description = docName,
            companyCode = null,
            isPoet = true,
            isCompleted = tags == "Completed",
            docNotes = docNotes,
            isCustomerFacing = isCustomerFacing,
        )
}

@Serializable
data class PoetDocumentAddress(
    val formStreet: String,
    val formCity: String,
    val formState: String,
    val formCountry: String,
    val formPostal: String,
) {
    companion object {
        fun from(docNotes: String): PoetDocumentAddress? {
            return try {
                val deserializer = Json { ignoreUnknownKeys = true }
                deserializer.decodeFromString<PoetDocumentAddress>(docNotes)
            } catch (_: Exception) {
                return null
            }
        }
    }
}

enum class Currency {
    USD,
    CAD,
}

data class PoetInvoice(
    val invoice: String,
    val description: String,
    val dueDate: ZonedDateTime,
    val invoiceAmount: Float,
    val remainingBalance: Float,
    val settledAmount: Float,
    val currency: Currency,
) {
    fun toInvoice(documents: List<PoetDocument>): Invoice {
        val document =
            documents.find { document ->
                document.invoiceNumber == invoice && document.docType == PoetDocumentType.MONTHLY_STATEMENT_OR_INVOICE
            }
        return Invoice(
            orderId = document?.orderId ?: "",
            documentId = document?.docRef?.substringAfterLast('/') ?: "",
            documentType = null,
            documentDescription = description,
            invoiceNumber = invoice,
            hasAttachment = false, // Hardcoded to false
            documentStatus = 0, // Hardcoded to zero since it cant be null
            currencyType = currency.toString(),
            totalDue = invoiceAmount,
            amountPaid = settledAmount,
            balanceDue = remainingBalance,
            isPaid = remainingBalance == 0.0F,
            dueDate = dueDate.toString(),
            companyCode = null,
            isInternal = false, // Hardcoded to false
            isPoet = true,
        )
    }
}

data class PoetUploadDocumentRequest(
    val customerId: String,
    val orderId: String,
    val file: String,
    val clientIp: String,
    val systemDateSigned: ZonedDateTime,
    val documentSigningService: String,
    val documentType: DocumentType,
    val id: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PoetCreditCard(
    @JsonView(IncludeInLogs::class)
    val creditCardId: String,
    val creditCardNum: String,
    val creditCardNumSecure: String,
    val creditCardProfileId: String,
    val podsCardBrandTxnId: String?,
    val expirationDate: LocalDateTime,
    val nameOnCard: String,
    @JsonView(IncludeInLogs::class)
    val creditCardType: String,
    @JsonView(IncludeInLogs::class)
    val isPrimary: Boolean,
    @JsonView(IncludeInLogs::class)
    val isActive: Boolean,
    val createdBy: String,
    val createdDateTime: LocalDateTime,
    val modifiedBy: String,
    @JsonView(IncludeInLogs::class)
    val modifiedDateTime: LocalDateTime,
    val billingAddress: PoetBillingAddressResponse?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PoetBillingAddressResponse(
    val street: String,
    val city: String,
    val state: String,
    val country: String,
    val zipCode: String,
    val role: String,
    val isPrimary: Boolean,
    val isActive: Boolean,
    val recId: Long,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PoetCreditLine(
    @JsonView(IncludeInLogs::class)
    val applicationId: Int,
    val balance: Double,
    val initialLoanAmount: Double,
    val originationDate: LocalDateTime,
    val terminationDate: LocalDateTime,
    @JsonView(IncludeInLogs::class)
    val isPrimary: Boolean,
    @JsonView(IncludeInLogs::class)
    val isActive: Boolean,
    @JsonView(IncludeInLogs::class)
    val lender: String,
    @JsonView(IncludeInLogs::class)
    val providerName: String,
    val createdBy: String,
    val createdDateTime: LocalDateTime,
    val modifiedBy: String,
    @JsonView(IncludeInLogs::class)
    val modifiedDateTime: LocalDateTime,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PoetPaymentMethodResponse(
    val creditCards: List<PoetCreditCard>,
    val creditLines: List<PoetCreditLine>,
)

data class PoetAddPaymentMethodRequest(
    val customerId: String,
    val paymentMethodType: PoetPaymentMethodType,
    val isPrimary: Boolean,
    val paymentMethodDetail: PoetPaymentMethodDetail,
)

data class PoetPaymentMethodDetail(
    val creditCard: PoetCreditCardToAdd?,
)

data class PoetCreditCardToAdd(
    val creditCardType: String,
    val maskedCreditCardNumber: String,
    val token: String,
    val expirationDate: LocalDate,
    val nameOnCard: String,
    val cardBrandTransactionId: String,
    val billingAddress: PoetPaymentBillingAddress,
)

data class PoetPaymentBillingAddress(
    val street: String,
    val city: String,
    val state: String,
    val countryCode: String,
    val zipCode: String,
)

enum class PoetPaymentMethodType {
    CREDIT_CARD,
    BANK_ACCOUNT,
}

data class PoetMakePaymentRequest(
    val customerId: String,
    val paymentMethodType: String,
    val paymentMethodId: String,
    val invoices: List<PoetInvoiceItem>,
)

data class PoetInvoiceItem(
    val invoiceId: String,
    val amount: Double,
)

data class PoetSetDefaultPaymentRequest(
    val customerId: String,
    val paymentMethodType: PoetPaymentMethodType,
    val paymentMethodId: String,
    val isPrimary: Boolean,
)

data class PoetContainerAvailabilityRequest(
    val zip: String,
    val moveLegType: MoveLegType,
    val orderType: PoetOrderType,
    val referenceDate: LocalDate?,
    val siteIdentity: String,
    val isIfOpenCalendar: Boolean = true,
)

data class PoetContainerAvailabilityResponse(
    val generalAvailability: List<PoetAvailabilityDate>,
    val eightFtContainerAvailability: List<PoetAvailabilityDate>,
    val twelveFtContainerAvailability: List<PoetAvailabilityDate>,
    val sixteenFtContainerAvailability: List<PoetAvailabilityDate>,
)

data class PoetAvailabilityDate(
    val date: LocalDate,
    val isAvailable: Boolean,
)

@JsonView(IncludeInLogs::class)
data class PoetServiceabilityRequest(
    val originZip: String,
    val destinationZip: String,
)

@JsonView(IncludeInLogs::class)
data class PoetServiceabilityResponse(
    val callInfo: PoetCallInfo,
    val postalCodeException: PoetPostalCodeException,
    val servicingLocations: List<PoetServicingLocation>,
) {
    fun isServiceable(): Boolean {
        val servicingLocation = servicingLocations.firstOrNull()
        return servicingLocation !== null &&
            servicingLocation.origination.warehouseId.equals(
                servicingLocation.destination.warehouseId,
                ignoreCase = true,
            )
    }
}

data class Timing(
    val startTime: String?,
    val endTime: String?,
)
typealias PoetWareHouseTimingsResponse = List<Map<String, Map<java.time.DayOfWeek, Timing>>>

@JsonView(IncludeInLogs::class)
data class PoetCallInfo(
    val statusCode: String,
    val statusMessage: String,
)

@JsonView(IncludeInLogs::class)
data class PoetPostalCodeException(
    val exceptionType: String?,
    val originExceptionType: String?,
    val destinationExceptionType: String?,
    val landingPageURL: String?,
)

data class PoetServicingLocation(
    val isSelected: Boolean,
    val orderType: PoetOrderType,
    val origination: WarehouseId,
    val destination: WarehouseId,
)

data class WarehouseId(
    val warehouseId: String,
)

enum class PoetDocumentStatus(
    val docStatus: String,
) {
    SENT("Sent"),
    COMPLETED("Completed"),
    DECOMMISSIONED("Decommissioned"),
    UNKNOWN("Unknown"),
    ;

    companion object {
        fun fromDocStatus(docStatus: String): PoetDocumentStatus {
            PoetDocumentStatus.entries.forEach { item ->
                if (item.docStatus.equals(docStatus, ignoreCase = true)) {
                    return item
                }
            }
            return UNKNOWN
        }
    }
}

enum class PoetDocumentType {
    ACH_AUTOPAY_AUTHORIZATION_FORM,
    ANCILLARY_ITEM_DOCUMENTATION,
    AUCTION_PAPERWORK,
    BAD_DEBT_PAPERWORK,
    BANKRUPTCY_PAPERWORK,
    BOOKING_CONFIRMATION,
    CBP_FORM_7533_INWARD_CARGO,
    CREDIT_CARD_AUTHORIZATION,
    CREDIT_CHECK_FORM,
    CREDIT_CARD_CHARGEBACKS,
    CERTIFICATE_OF_INSURANCE,
    RENTAL_AGREEMENT,
    IF_RENTAL_AGREEMENT,
    LOCAL_RENTAL_AGREEMENT,
    IF_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE,
    LOCAL_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE,
    CUSTOMER_INCIDENT_NOTE,
    DAMAGE_WAIVERS_AND_RELEASES,
    DAMAGE_PICTURES,
    COPY_OF_DEATH_CERTIFICATE,
    DEED_OR_LEASE_VISITOR,
    DEFAULT_ON_PAYMENT_LETTER,
    UNACCOMPANIED_ARTICLES,
    ADD_A_GENERAL_FORM_TO_CAPTURE_OTHER_FORMS,
    SPONGY_MOTH_FORM,
    HOUSEHOLD_GOODS_INVENTORY_LIST,
    IMAGE_FILE,
    INVENTORY_ACKNOWLEDGEMENT_OF_HAZARDOUS_MATERIALS,
    RETAIL_CUSTOMER_INVOICE,
    LEGAL_CORRESPONDENCE,
    LEGAL_STATUS,
    MASTER_RENTAL_AGREEMENT,
    CERTIFIED_NAME_CHANGE,
    FRAGILE_AND_NON_PAVED_SURFACE_WAIVER,
    GENERAL_NOTE_WITH_FILE_ATTACHMENT,
    GENERAL_NOTE_WITH_IMAGE_FILE_ATTACHMENT,
    OCEAN_TRANSPORT_QUOTE,
    ORDER_CONFIRMATION,
    ORDER_UPDATE_CONFIRMATION,
    COPY_OF_PASSPORTS,
    ATTACH_PDF_FILE,
    FORM_B4A_PERSONAL_EFFECTS_ACCOUNTING,
    FORM_B4E_PERSONAL_EFFECTS_ACCOUNTING_DOCUMENT,
    MONTHLY_STATEMENT_OR_INVOICE,
    PLACEMENT_WAIVER,
    FORM_5291_CUSTOMS_POWER_OF_ATTORNEY,
    PODS_CANADA_CUSTOMS_FORM,
    PURPOSE_OF_THE_MOVE_AND_CONTACT_PHONE_NUMBER_DOCUMENT,
    SETTLEMENT_AGREEMENT,
    SPOTTED_LANTERN_FLY_FORM,
    FORM_II_RC_159_SUPPLEMENTAL_DECLARATION,
    DEED_OR_LEASE_VACATION,
    W9,
    COPY_OF_WILL_AND_OR_TRUST,
    MILITARY_WEIGHT_TICKET_EMPTY,
    MILITARY_WEIGHT_TICKET_EMPTY_WITH_TRUCK,
    MILITARY_WEIGHT_TICKET_FULL,
    MILITARY_WEIGHT_TICKET_FULL_WITH_TRUCK,
    UNKNOWN,
}

@JsonView(IncludeInLogs::class)
data class GenerateOrderChangesRequest(
    val orderId: String,
    val containerOrderId: String,
    val requestedDate: LocalDate,
    val moveLegId: String,
    val requestedServiceAddress: PoetMoveLegAddress?,
    val isRequestedServiceForOrigin: Boolean,
    val containerPlacement: ContainerPlacement?,
    val locationAvailabilityFields: PoetLocationAvailabilityFields
)

@JsonView(IncludeInLogs::class)
data class PoetLocationAvailabilityFields(
    val zipCode: String,
    val moveLegType: MoveLegType,
    val orderType: PoetOrderType,
    val siteIdentity: String,
    val isIfOpenCalendar: Boolean,
    val channel: String = "WEB",
    val customerType: CustomerType? = CustomerType.RESIDENTIAL,
    val referenceDate: LocalDate? = null,
    val sessionId: String? = null,
    val custTrackingId: String? = null,
)

@JsonView(IncludeInLogs::class)
data class AddWarehouseVisitRequest(
    val orderId: String,
    val containerOrderId: String,
    val moveLegIdToAddAfter: String,
    val visitDate: LocalDate,
    val locationAvailabilityFields: PoetLocationAvailabilityFields,
)

@JsonView(IncludeInLogs::class)
data class RemoveWarehouseVisitRequest(
    val orderId: String,
    val containerOrderId: String,
    val moveLegId: String,
)

@JsonView(IncludeInLogs::class)
data class GenerateQuoteForOrderChangesResponse(
    val sfQuoteId: String,
    val priceDifference: Long,
) {
    fun toOrderChangesResponse() = UpdateMoveLegResponse(sfQuoteId, String.format("%.2f", priceDifference / 100.0))
}

@JsonView(IncludeInLogs::class)
data class ApplyOrderChangesRequest(
    val sfQuoteId: String,
)

@JsonView(IncludeInLogs::class)
data class ApplyQuoteToOrderResponse(
    val sfQuoteId: String,
) {
    fun toOrderChangesResponse() = UpdateMoveLegResponse(null, null)
}

data class PoetInvoicePaymentResponse(
    @JsonView(IncludeInLogs::class)
    val errors: List<PoetInvoicePaymentError>,
)

enum class PoetInvoicePaymentErrorEnum(
    val payInvoiceCreditCardException: PayInvoiceCreditCardException,
) {
    FRAUD_SUSPECTED(PayInvoiceCreditCardException.fraudSuspected()),
    CARD_CLOSED(PayInvoiceCreditCardException.cardClosed()),
    INACTIVE_CARD(PayInvoiceCreditCardException.inactiveCard()),
    INVALID_ACCOUNT(PayInvoiceCreditCardException.invalidAccount()),
    INSUFFICIENT_FUNDS(PayInvoiceCreditCardException.insufficientFunds()),
    PROCESSOR_DECLINED(PayInvoiceCreditCardException.processorDeclined()),
    CARD_ISSUER_DECLINED(PayInvoiceCreditCardException.cardIssuerDeclined()),
    BANK_PAYMENT_UNAUTHORISED(PayInvoiceCreditCardException.bankPaymentUnauthorised()),
    PAYPAL_ACCOUNT_ISSUE(PayInvoiceCreditCardException.payPalAccountIssue()),
    LIMIT_EXCEEDED(PayInvoiceCreditCardException.limitExceeded()),
    NO_BALANCE_REMAINING(PayInvoiceCreditCardException.fullySettled()),
    DECLINED(PayInvoiceCreditCardException.declined()),
    UNKNOWN(PayInvoiceCreditCardException.unknown()),
}

data class PoetInvoicePaymentError(
    val invoiceId: String,
    val errorReason: PoetInvoicePaymentErrorEnum,
) {
    fun toPayInvoiceException(): PayInvoiceCreditCardException = this.errorReason.payInvoiceCreditCardException
}
