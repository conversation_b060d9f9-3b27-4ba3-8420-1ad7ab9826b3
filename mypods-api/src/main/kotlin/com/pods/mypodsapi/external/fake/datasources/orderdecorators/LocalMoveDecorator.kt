package com.pods.mypodsapi.external.fake.datasources.orderdecorators

import com.pods.mypodsapi.external.fake.datasources.IDataSource
import com.pods.mypodsapi.external.fake.datasources.modifyMoveLeg
import com.pods.mypodsapi.external.fake.datasources.withContainerId
import com.pods.mypodsapi.external.fake.datasources.withContainers
import com.pods.mypodsapi.external.locum.entities.LocumCustomerOrdersResponse
import java.time.LocalDateTime

class LocalMoveDecorator(private val source: IDataSource<LocumCustomerOrdersResponse>) :
    IDataSource<LocumCustomerOrdersResponse> {
    override fun getData(): LocumCustomerOrdersResponse {
        val order = source.getData()
        val firstOrder = order.response!!.first()
        val today = LocalDateTime.now()
        val future = today.plusWeeks(1)
        val past = today.minusWeeks(1)
        val baseContainer = firstOrder.containers.first()
            .modifyMoveLeg(0) { it.copy(scheduledDate = today) }
            .modifyMoveLeg(1) { it.copy(scheduledDate = null) }
            .modifyMoveLeg(2) { it.copy(scheduledDate = null) }

        val containers = listOf(
            baseContainer.modifyMoveLeg(0) { it.copy(scheduledDate = today) }
                .withContainerId("initial delivery today"),
            baseContainer.modifyMoveLeg(0) { it.copy(scheduledDate = future) }
                .withContainerId("initial delivery future"),
            baseContainer.modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .withContainerId("initial delivery past"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = today) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = future) }
                .withContainerId("move future"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = today) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = future) }
                .withContainerId("final pickup future"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = today) }
                .withContainerId("final pickup today"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = past) }
                .withContainerId("final pickup past"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = future) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = future) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = future) }
                .withContainerId("all scheduled future"),
            baseContainer
                .modifyMoveLeg(0) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(1) { it.copy(scheduledDate = past) }
                .modifyMoveLeg(2) { it.copy(scheduledDate = today) }
                .withContainerId("most scheduled past"),
        )
        return order.withContainers(containers)
    }

}