package com.pods.mypodsapi.external.featureFlags

import com.pods.mypodsapi.customers.CustomerType
import io.split.client.SplitClient
import org.springframework.stereotype.Service

@Service
class EventTrackingService(
    private val splitClient: SplitClient,
) {
    fun startUserOnboarding(
        splitKey: String,
        customerType: CustomerType?,
    ) {
        if (customerType == null) {
            sendTrackingEvent(splitKey, PASSWORD_ONBOARDING_START)
        } else {
            sendTrackingEvent(splitKey, PASSWORD_ONBOARDING_START, mapOf("customer_type" to customerType))
        }
    }

    fun finishUserOnboarding(
        splitKey: String,
        customerType: CustomerType?,
    ) {
        if (customerType == null) {
            sendTrackingEvent(splitKey, PASSWORD_ONBOARDING_SUCCESS)
        } else {
            sendTrackingEvent(splitKey, PASSWORD_ONBOARDING_SUCCESS, mapOf("customer_type" to customerType))
        }
    }

    private fun sendTrackingEvent(
        key: String,
        event: String,
    ) {
        splitClient.track(key, SPLIT_TRAFFIC_TYPE, event)
    }

    private fun sendTrackingEvent(
        key: String,
        event: String,
        properties: Map<String, Any>,
    ) {
        splitClient.track(key, SPLIT_TRAFFIC_TYPE, event, properties)
    }

    private companion object {
        const val SPLIT_TRAFFIC_TYPE = "user"
        const val PASSWORD_ONBOARDING_START = "password_onboarding_start"
        const val PASSWORD_ONBOARDING_SUCCESS = "password_onboarding_success"
    }
}
