package com.pods.mypodsapi.external.featureFlags

import com.pods.mypodsapi.config.filters.getLogger
import io.split.client.SplitClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class FeatureFlagService(
    private val splitClient: SplitClient,
    @Value("\${poet-facade.enabled}") private val poetEnabled: String,
) {
    val logger = getLogger()

    fun maintenanceModeEnabled(visitorId: String?): Boolean = getFeatureFlag(MAINTENANCE_MODE, visitorId ?: DEFAULT_KEY)

    fun fnpsEnabled(): Boolean = getFeatureFlag(MYPODS_FNPS, DEFAULT_KEY)

    fun acornFinancingWidgetEnabled(customerId: String): Boolean = getFeatureFlag(ENABLE_ACORN_WIDGETS, customerId)

    fun poetFacadeEnabled(customerId: String): Boolean =
        customerId.first() == '2' || poetEnabled == "true" || getFeatureFlag(POET_ENABLED, customerId)

    fun passwordOnboardingEnabled(customerId: String): Boolean = getFeatureFlag(PASSWORD_ONBOARDING, customerId)

    private fun getFeatureFlag(
        flagName: String,
        userIdentifier: String,
    ): Boolean =
        try {
            splitClient.getTreatment(userIdentifier, flagName) != FEATURE_DISABLED
        } catch (e: Exception) {
            logger.error("Split failed to give a good value. Returning false.", e)
            false
        }

    companion object {
        const val DEFAULT_KEY = "mypods-v2"
        const val MAINTENANCE_MODE = "mypods_maintenance_mode"
        const val MYPODS_FNPS = "MyPods_FNPS"
        const val ENABLE_ACORN_WIDGETS = "mypods_acorn_financing"
        const val POET_ENABLED = "poet_enabled"
        const val FEATURE_DISABLED = "off"
        private const val PASSWORD_ONBOARDING = "pb-password-onboarding"
    }
}
