package com.pods.mypodsapi.external.locum.mappers

import com.pods.mypodsapi.authorization.UnsignedRentalAgreementIdentity
import com.pods.mypodsapi.customers.Customer
import com.pods.mypodsapi.customers.CustomerAddress
import com.pods.mypodsapi.customers.CustomerType
import com.pods.mypodsapi.customers.Email
import com.pods.mypodsapi.customers.LegacyOrderData
import com.pods.mypodsapi.customers.Phone
import com.pods.mypodsapi.customers.SecurityQuestionAnswer
import com.pods.mypodsapi.customers.UpdateBillingAddressRequest
import com.pods.mypodsapi.customers.UpdateEmailRequest
import com.pods.mypodsapi.customers.UpdatePrimaryPhoneRequest
import com.pods.mypodsapi.customers.UpdateSecondaryPhoneRequest
import com.pods.mypodsapi.customers.UpdateShippingAddressRequest
import com.pods.mypodsapi.customers.UpdateSmsOptInRequest
import com.pods.mypodsapi.document.Document
import com.pods.mypodsapi.document.findDocumentByType
import com.pods.mypodsapi.external.locum.entities.CustomerResponse
import com.pods.mypodsapi.external.locum.entities.LocumCustomerAddress
import com.pods.mypodsapi.external.locum.entities.LocumEmail
import com.pods.mypodsapi.external.locum.entities.LocumLegacyOrderData
import com.pods.mypodsapi.external.locum.entities.LocumPhone
import com.pods.mypodsapi.external.locum.entities.LocumSecurityQuestion
import com.pods.mypodsapi.external.locum.entities.LocumUpdateCustomerRequest
import com.pods.mypodsapi.external.locum.entities.LocumUpdateSmsOptInRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetDocumentType
import com.pods.mypodsapi.orders.Order
import com.pods.mypodsapi.orders.OrderType

// region Customer
@Suppress("TooManyFunctions")
val emptyWeightTicketTypes =
    listOf(
        PoetDocumentType.MILITARY_WEIGHT_TICKET_EMPTY.toString(),
        PoetDocumentType.MILITARY_WEIGHT_TICKET_EMPTY_WITH_TRUCK.toString(),
    )

val filledWeightTicketTypes =
    listOf(
        PoetDocumentType.MILITARY_WEIGHT_TICKET_FULL.toString(),
        PoetDocumentType.MILITARY_WEIGHT_TICKET_FULL_WITH_TRUCK.toString(),
    )

val rentalAgreementTypes =
    listOf(
        PoetDocumentType.RENTAL_AGREEMENT.toString(),
        PoetDocumentType.IF_RENTAL_AGREEMENT.toString(),
        PoetDocumentType.LOCAL_RENTAL_AGREEMENT.toString(),
        PoetDocumentType.IF_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE.toString(),
        PoetDocumentType.LOCAL_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE.toString(),
    )

fun CustomerResponse.toCustomer(isConverted: Boolean, trackingUuid: String, username: String) =
    Customer(
        id = customer.username.toString(),
        firstName = customer.firstName ?: DEFAULT_FIRST_NAME,
        lastName = customer.lastName ?: DEFAULT_LAST_NAME,
        email = customer.email?.toEmail(),
        customerType = customer.customerType.toCustomerType(),
        primaryPhone = customer.primaryPhone?.toPhone(),
        secondaryPhone = customer.secondaryPhone?.toPhone(),
        billingAddress = customer.defaultMailingAddress?.toAddress(),
        shippingAddress = customer.defaultStreetAddress?.toAddress(),
        isConverted = isConverted,
        smsOptIn = smsPreferences.smsOptIn,
        securityQuestionAnswer = securityQuestionAnswer.toSecurityQuestionAnswer(),
        trackingUuid = trackingUuid,
        username = username.ifEmpty { null },
    )

private fun LocumEmail.toEmail() = Email(identity, address)

private fun LocumPhone.toPhone() = Phone(identity, number)

private fun String?.toCustomerType(): CustomerType {
    if (this.equals(CustomerType.COMMERCIAL.value, true)) {
        return CustomerType.COMMERCIAL
    }
    return CustomerType.RESIDENTIAL
}

private fun LocumCustomerAddress.toAddress() =
    CustomerAddress(
        id = identity,
        addressType = addressType,
        address1 = address1,
        address2 = address2,
        city = city,
        state = state,
        postalCode = postalCode,
        regionCode = regionCode,
    )

private fun LocumSecurityQuestion.toSecurityQuestionAnswer() =
    SecurityQuestionAnswer(
        question = question,
        answer = answer,
    )

fun List<LocumLegacyOrderData>.toLegacyOrderData(): List<LegacyOrderData> =
    this.map {
        LegacyOrderData(
            orderId = it.orderId,
            companyCode = it.companyCode,
            rentalAgreementAccepted = it.rentalAgreementAccepted,
            invasiveMothAgreementAccepted = it.invasiveMothAgreementAccepted,
            emptyWeightTicketId = it.emptyWeightTicketId,
            filledWeightTicketId = it.filledWeightTicketId,
            identity = it.identity,
            orderType = OrderType.UNSPECIFIED, // don't get order type for locum - the url is filled in by company code
        )
    }

fun List<Order>.poetToLegacyOrderData(orderDocuments: List<Document>): List<LegacyOrderData> =
    this.map {
        val spongyMothForm = orderDocuments.findDocumentByType(listOf(PoetDocumentType.SPONGY_MOTH_FORM.toString()), it.orderId)
        val spongyMothFormSigned = (spongyMothForm == null || spongyMothForm.isCompleted)

        val emptyWeightTicketId =
            orderDocuments
                .findDocumentByType(emptyWeightTicketTypes, it.orderId)
                ?.id
                ?.toLong()
                ?: 0

        val filledWeightTicketId =
            orderDocuments
                .findDocumentByType(filledWeightTicketTypes, it.orderId)
                ?.id
                ?.toLong()
                ?: 0
        val rentalAgreementDocId =
            orderDocuments
                .findDocumentByType(rentalAgreementTypes, it.orderId)
                ?.id

        LegacyOrderData(
            orderId = it.orderId,
            companyCode = it.billingCompanyCode ?: "",
            rentalAgreementAccepted = it.rentalAgreementSigned,
            rentalAgreementDocId = rentalAgreementDocId,
            invasiveMothAgreementAccepted = spongyMothFormSigned,
            emptyWeightTicketId = emptyWeightTicketId,
            filledWeightTicketId = filledWeightTicketId,
            identity = UnsignedRentalAgreementIdentity.DEFAULT_UNSIGNED.documentCode,
            orderType = if (it.orderType == OrderType.IF) OrderType.IF else OrderType.LOCAL,
        )
    }

// endregion LocumCustomer.toCustomer()

// region UpdateCustomerRequest.toLocumRequest()

fun UpdateEmailRequest.toLocumRequest(): LocumUpdateCustomerRequest =
    LocumUpdateCustomerRequest(
        email = email.toLocumEmail(),
    )

fun UpdatePrimaryPhoneRequest.toLocumRequest(): LocumUpdateCustomerRequest =
    LocumUpdateCustomerRequest(
        primaryPhone = phone.toLocumPhone(),
    )

fun UpdateSecondaryPhoneRequest.toLocumRequest(): LocumUpdateCustomerRequest =
    LocumUpdateCustomerRequest(
        secondaryPhone = phone.toLocumPhone(),
    )

fun UpdateBillingAddressRequest.toLocumRequest(): LocumUpdateCustomerRequest =
    LocumUpdateCustomerRequest(
        defaultMailingAddress = address.toLocumCustomerAddress(),
    )

fun UpdateShippingAddressRequest.toLocumRequest(): LocumUpdateCustomerRequest =
    LocumUpdateCustomerRequest(
        defaultStreetAddress = address.toLocumCustomerAddress(),
    )

private fun CustomerAddress.toLocumCustomerAddress(): LocumCustomerAddress =
    LocumCustomerAddress(
        identity = id,
        addressType = addressType,
        address1 = address1,
        address2 = address2,
        city = city,
        state = state,
        postalCode = postalCode,
        regionCode = regionCode,
    )

private fun Email.toLocumEmail() = LocumEmail(id, address)

private fun Phone.toLocumPhone() = LocumPhone(id, number)

// endregion UpdateCustomerRequest.toLocumRequest()

fun UpdateSmsOptInRequest.toLocumRequest(customerId: String): LocumUpdateSmsOptInRequest =
    LocumUpdateSmsOptInRequest(
        customerId = customerId,
        lastName = this.lastName,
        phoneNumber = this.primaryPhone?.number ?: this.secondaryPhone?.number!!,
        optStatus = this.newSmsOptIn,
    )

const val DEFAULT_FIRST_NAME = "Web"
const val DEFAULT_LAST_NAME = "Customer"
