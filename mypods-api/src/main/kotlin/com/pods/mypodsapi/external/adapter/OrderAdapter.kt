package com.pods.mypodsapi.external.adapter

import com.pods.mypodsapi.PodsException
import com.pods.mypodsapi.customers.LegacyOrderData
import com.pods.mypodsapi.document.Document
import com.pods.mypodsapi.external.featureFlags.FeatureFlagService
import com.pods.mypodsapi.external.locum.LocumClient
import com.pods.mypodsapi.external.locum.entities.LocumUpdateMoveLegResponse
import com.pods.mypodsapi.external.locum.mappers.toLegacyOrderData
import com.pods.mypodsapi.external.locum.mappers.toLocumRequest
import com.pods.mypodsapi.external.locum.mappers.toOrder
import com.pods.mypodsapi.external.locum.mappers.toUpdateMoveLegResponse
import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import com.pods.mypodsapi.external.poetFacade.entities.ApplyOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.ApplyQuoteToOrderResponse
import com.pods.mypodsapi.external.poetFacade.entities.PoetWareHouseTimingsResponse
import com.pods.mypodsapi.external.poetFacade.mappers.toPoetRequest
import com.pods.mypodsapi.external.poetFacade.toAddWarehouseVisitRequest
import com.pods.mypodsapi.external.poetFacade.toGenerateOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.toRemoveWarehouseVisitRequest
import com.pods.mypodsapi.orders.IOrderService
import com.pods.mypodsapi.orders.MoveLegType
import com.pods.mypodsapi.orders.Order
import com.pods.mypodsapi.orders.SameServiceAreaRequest
import com.pods.mypodsapi.orders.UpdateMoveLegRequest
import com.pods.mypodsapi.orders.UpdateMoveLegResponse
import com.pods.mypodsapi.orders.WarehouseHoursService
import kotlinx.coroutines.reactive.awaitFirst
import kotlinx.coroutines.reactive.awaitFirstOrNull
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.time.LocalDate

const val STALE_DATA_ERROR_MESSAGE = "Data is stale. Please reload the page."

@Service
class OrderAdapter(
    private val locumClient: LocumClient,
    private val featureFlagService: FeatureFlagService,
    private val poetFacadeClient: PoetFacadeClient,
    private val warehouseHoursService: WarehouseHoursService,
) : IOrderService {
    override suspend fun getLegacyOrderData(customerId: String): List<LegacyOrderData> {
        val orderData =
            locumClient
                .legacyAuthData(customerId, customerId)
                .awaitFirstOrNull()
                ?.response
                ?.toLegacyOrderData() ?: emptyList()
        return orderData
    }

    override suspend fun getOrdersByCustomerId(customerId: String): List<Order> {
        if (featureFlagService.poetFacadeEnabled(customerId)) {
            val poetOrders =
                poetFacadeClient
                    .getOrdersByCustomerId(customerId)
                    .awaitFirstOrNull()
                    ?: emptyList()
            return poetOrders
                .filter { it.status.isActive }
                .map { it.toOrder() }
                .filter { it.containers.isNotEmpty() }
                .getWarehouseTimings()
        }
        val orders =
            locumClient
                .getOrdersByCustomerId(customerId)
                .awaitFirstOrNull()
                ?.response
                ?: emptyList()
        return orders.map { it.toOrder() }
    }

    suspend fun List<Order>.getWarehouseTimings(): List<Order> {
        // make warehouseTimings call with all siteIdentities
        val warehousesTimings = warehouseHoursService.getWarehouseTimings()

        // / Update moveLeg eta with the timings based on the day they are scheduled
        updateMovelegsWithETA(warehousesTimings)
        return this
    }

    private fun List<Order>.updateMovelegsWithETA(warehousesTimings: PoetWareHouseTimingsResponse?) {
        warehousesTimings?.takeIf { it.isNotEmpty() }?.let { timings ->
            this.forEach { order ->
                order.containers.forEach { container ->
                    container.moveLegs =
                        container.moveLegs.map { moveLeg ->
                            moveLeg.copy(
                                eta =
                                    calculateEta(
                                        moveLeg.siteIdentity,
                                        moveLeg.scheduledDate,
                                        timings,
                                    ),
                            )
                        }
                }
            }
        }
    }

    private fun calculateEta(
        warehouseId: String,
        scheduledDate: LocalDate?,
        warehousesTimingsResponse: PoetWareHouseTimingsResponse?,
    ): String? {
        if (warehousesTimingsResponse == null || scheduledDate == null) {
            return null
        }

        for (warehouse in warehousesTimingsResponse) {
            val timings = warehouse[warehouseId]?.get(scheduledDate.dayOfWeek)
            if (timings != null && timings.startTime != null && timings.endTime != null) {
                return timings.startTime + " - " + timings.endTime
            }
        }
        return null
    }

    override suspend fun getOrderDocumentsForCustomer(customerId: String): List<Document> {
        if (featureFlagService.poetFacadeEnabled(customerId)) {
            val poetOrderDocuments =
                poetFacadeClient
                    .getOrderDocuments(customerId)
                    .awaitFirstOrNull()
                    ?: emptyList()
            return poetOrderDocuments.map { it.toDocument() }
        } else {
            // TODO remove once locum is gone
            return emptyList()
        }
    }

    override suspend fun updateMoveLeg(
        customerId: String,
        request: UpdateMoveLegRequest,
    ): UpdateMoveLegResponse {
        if (featureFlagService.poetFacadeEnabled(customerId)) {
            val response =
                try {
                    when {
                        request.moveLegType == MoveLegType.VISIT_CONTAINER && !request.isCancelLeg -> {
                            poetFacadeClient
                                .addWarehouseVisit(request.toAddWarehouseVisitRequest())
                                .awaitFirst()
                        }
                        request.moveLegType == MoveLegType.VISIT_CONTAINER && request.isCancelLeg -> {
                            poetFacadeClient
                                .cancelWarehouseVisit(request.toRemoveWarehouseVisitRequest())
                                .awaitFirst()
                        }
                        else -> {
                            poetFacadeClient
                                .generateOrderChanges(request.toGenerateOrderChangesRequest())
                                .awaitFirstOrNull()!!
                        }
                    }
                } catch (e: WebClientResponseException.BadRequest) {
                    throw PodsException.badRequest(
                        internalMessage = e.message.toString(),
                        externalMessage = STALE_DATA_ERROR_MESSAGE,
                        cause = e,
                    )
                }

            if (response.priceDifference != 0L) {
                return response.toOrderChangesResponse()
            }

            val applyToOrderResponse =
                try {
                    poetFacadeClient
                        .applyQuoteToOrder(ApplyOrderChangesRequest(response.sfQuoteId))
                        .awaitFirstOrNull()!!
                } catch (e: WebClientResponseException.BadRequest) {
                    throw PodsException.badRequest(
                        internalMessage = e.message.toString(),
                        externalMessage = STALE_DATA_ERROR_MESSAGE,
                        cause = e,
                    )
                }

            return applyToOrderResponse.toOrderChangesResponse()
        }

        val response: LocumUpdateMoveLegResponse =
            try {
                locumClient
                    .updateMoveLeg(request.toLocumRequest(customerId))
                    .awaitFirst()
            } catch (e: WebClientResponseException.BadRequest) {
                throw PodsException.badRequest(
                    internalMessage = e.message.toString(),
                    externalMessage = STALE_DATA_ERROR_MESSAGE,
                    cause = e,
                )
            }

        response.validateCallInfo()
        return response.toUpdateMoveLegResponse()
    }

    override suspend fun applyQuoteToOrder(
        customerId: String,
        request: ApplyOrderChangesRequest,
    ): ApplyQuoteToOrderResponse {
        if (featureFlagService.poetFacadeEnabled(customerId)) {
            val applyToOrderResponse =
                try {
                    poetFacadeClient
                        .applyQuoteToOrder(request)
                        .awaitFirstOrNull()!!
                } catch (e: WebClientResponseException.BadRequest) {
                    throw PodsException.badRequest(
                        internalMessage = e.message.toString(),
                        externalMessage = STALE_DATA_ERROR_MESSAGE,
                        cause = e,
                    )
                }

            return applyToOrderResponse
        } else {
            throw PodsException.notFound(internalMessage = "Apply quote to order is not implemented for non-poet customers")
        }
    }

    override suspend fun isSameServiceArea(
        request: SameServiceAreaRequest,
        customerId: String,
    ): Boolean =
        if (featureFlagService.poetFacadeEnabled(customerId)) {
            poetFacadeClient
                .getServiceability(request.toPoetRequest())
                .awaitFirst()
                .isServiceable()
        } else {
            locumClient
                .isSameServiceAddress(request.toLocumRequest())
                .awaitFirst()
                .isSameServiceArea
        }
}
