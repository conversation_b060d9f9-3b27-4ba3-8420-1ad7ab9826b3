@file:Suppress("TooManyFunctions")

package com.pods.mypodsapi.external.poetFacade

import com.pods.mypodsapi.document.AcceptRentalAgreementRequest
import com.pods.mypodsapi.document.CustomStatementRequest
import com.pods.mypodsapi.document.CustomerDocument
import com.pods.mypodsapi.document.Document
import com.pods.mypodsapi.document.DocumentType
import com.pods.mypodsapi.document.DocumentType.FRAGILE_AND_NON_PAVED_SURFACE_WAIVER
import com.pods.mypodsapi.document.DocumentType.RENTAL_AGREEMENT
import com.pods.mypodsapi.document.OrderDocument
import com.pods.mypodsapi.document.SignFnpsWaiverRequest
import com.pods.mypodsapi.external.poetFacade.entities.AddWarehouseVisitRequest
import com.pods.mypodsapi.external.poetFacade.entities.GenerateOrderChangesRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetCustomStatementRequest
import com.pods.mypodsapi.external.poetFacade.entities.PoetDocument
import com.pods.mypodsapi.external.poetFacade.entities.PoetDocumentStatus
import com.pods.mypodsapi.external.poetFacade.entities.PoetDocumentType
import com.pods.mypodsapi.external.poetFacade.entities.PoetLocationAvailabilityFields
import com.pods.mypodsapi.external.poetFacade.entities.PoetMoveLegAddress
import com.pods.mypodsapi.external.poetFacade.entities.PoetUploadDocumentRequest
import com.pods.mypodsapi.external.poetFacade.entities.RemoveWarehouseVisitRequest
import com.pods.mypodsapi.orders.LocationAvailabilityFields
import com.pods.mypodsapi.orders.OrderType.Companion.toPoetOrderType
import com.pods.mypodsapi.orders.ServiceAddress
import com.pods.mypodsapi.orders.UpdateMoveLegRequest
import com.pods.mypodsapi.utils.Constants
import java.time.LocalDate
import java.util.Base64

fun CustomStatementRequest.toPoetRequest(customerId: String) =
    PoetCustomStatementRequest(
        customerId = customerId,
        startDate = startDate,
        endDate = endDate,
    )

fun List<PoetDocument>.toDocuments(): List<Document> =
    this.map {
        // TODO: Need to map properties correctly.
        Document(
            orderId = it.orderId ?: "",
            id = it.docRef.substringAfterLast("/"),
            type = it.docType.toString(),
            description = if (it.docType == PoetDocumentType.RENTAL_AGREEMENT) "Rental Agreement" else it.docName,
            companyCode = "",
            isPoet = true,
            isCustomerFacing = it.isCustomerFacing,
        )
    }

fun List<PoetDocument>.toOrderDocuments(): List<OrderDocument> = this.map { it.toOrderDocument() }

fun PoetDocument.toOrderDocument(): OrderDocument =
    OrderDocument(
        orderId = orderId ?: "",
        docPath = docRef,
        docName = docRef.substringAfterLast("/"),
        id = id,
        isCustomerFacing = isCustomerFacing,
        docType = DocumentType.valueOf(docType.toString()),
        docStatus = PoetDocumentStatus.fromDocStatus(tags ?: ""),
        billingCompanyCode = null,
        title = if (DocumentType.valueOf(docType.toString()) == RENTAL_AGREEMENT) "Rental Agreement" else docName,
        isInterFranchise = null,
    )

fun List<PoetDocument>.toCustomerDocuments(): List<CustomerDocument> =
    this.map {
        CustomerDocument(
            docRef = it.docRef,
            docName = it.docRef.substringAfterLast("/"),
            id = it.id,
            isCustomerFacing = it.isCustomerFacing,
            docType = DocumentType.valueOf(it.docType.toString()),
            tags = it.tags,
            docNotes = it.docNotes,
            title = it.docName,
        )
    }

fun SignFnpsWaiverRequest.toPoetRequest() =
    PoetUploadDocumentRequest(
        customerId = customerId,
        orderId = orderId.toString(),
        file = documentData,
        documentSigningService = Constants.APP_URL,
        systemDateSigned = systemDateSigned,
        clientIp = clientIp,
        documentType = FRAGILE_AND_NON_PAVED_SURFACE_WAIVER,
        id = null,
    )

fun AcceptRentalAgreementRequest.toPoetRequest(
    customerId: String,
    clientIp: String,
    stampedFile: ByteArray,
): PoetUploadDocumentRequest =
    PoetUploadDocumentRequest(
        customerId = customerId,
        orderId = orderId,
        file = Base64.getEncoder().encodeToString(stampedFile),
        documentSigningService = Constants.APP_URL,
        systemDateSigned = dateSigned,
        clientIp = clientIp,
        documentType = RENTAL_AGREEMENT,
        id = docId,
    )

fun UpdateMoveLegRequest.toGenerateOrderChangesRequest() =
    GenerateOrderChangesRequest(
        orderId = orderId,
        containerOrderId = containerOrderId!!,
        requestedDate = requestedDate!!,
        moveLegId = moveLegId,
        requestedServiceAddress = serviceAddress?.toPoetMoveLegAddress(),
        isRequestedServiceForOrigin = moveLegType.isPickupType,
        containerPlacement = containerPlacement,
        locationAvailabilityFields = locationFields.toPoetLocationAvailabilityFields(requestedDate),
    )

fun LocationAvailabilityFields.toPoetLocationAvailabilityFields(requestedDate: LocalDate?) = PoetLocationAvailabilityFields(
    zipCode = zip,
    moveLegType = moveLegType,
    orderType = orderType.toPoetOrderType(),
    siteIdentity = siteIdentity,
    isIfOpenCalendar = isIfOpenCalendar,
    channel = channel,
    referenceDate = requestedDate,
    customerType = customerType,
)

fun UpdateMoveLegRequest.toAddWarehouseVisitRequest() =
    AddWarehouseVisitRequest(
        orderId = orderId,
        containerOrderId = containerOrderId!!,
        moveLegIdToAddAfter = moveLegId,
        visitDate = requestedDate!!,
        locationAvailabilityFields = locationFields.toPoetLocationAvailabilityFields(requestedDate),
    )

fun UpdateMoveLegRequest.toRemoveWarehouseVisitRequest() =
    RemoveWarehouseVisitRequest(
        orderId = orderId,
        containerOrderId = containerOrderId!!,
        moveLegId = moveLegId,
    )

fun ServiceAddress.toPoetMoveLegAddress() =
    PoetMoveLegAddress(
        address1 = address1,
        address2 = address2,
        city = city,
        state = state,
        postalCode = postalCode,
        country = country,
        isStorageCenter = null,
    )
