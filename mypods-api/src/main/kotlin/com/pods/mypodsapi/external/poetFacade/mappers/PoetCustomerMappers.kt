package com.pods.mypodsapi.external.poetFacade.mappers

import com.pods.mypodsapi.customers.Customer
import com.pods.mypodsapi.customers.CustomerAddress
import com.pods.mypodsapi.customers.Email
import com.pods.mypodsapi.customers.Phone
import com.pods.mypodsapi.customers.UpdateAccountRequest
import com.pods.mypodsapi.customers.UpdateBillingAddressRequest
import com.pods.mypodsapi.customers.UpdatePrimaryPhoneRequest
import com.pods.mypodsapi.customers.UpdateSecondaryPhoneRequest
import com.pods.mypodsapi.customers.UpdateShippingAddressRequest
import com.pods.mypodsapi.customers.UpdateSmsOptInRequest
import com.pods.mypodsapi.external.locum.entities.LocumAddressType
import com.pods.mypodsapi.external.poetFacade.entities.PoetAccountAddress
import com.pods.mypodsapi.external.poetFacade.entities.PoetAccountPhone
import com.pods.mypodsapi.external.poetFacade.entities.PoetCustomer
import com.pods.mypodsapi.external.poetFacade.entities.PoetServiceabilityRequest
import com.pods.mypodsapi.orders.SameServiceAreaRequest

fun PoetCustomer.toCustomer(
    trackingUuid: String,
    username: String
) = Customer(
    id = cuid,
    trackingUuid = trackingUuid,
    username = username,
    firstName = firstName,
    lastName = lastName,
    customerType = customerType,
    email = Email(id = 1, address = email),
    primaryPhone = primaryPhone?.toPhone(),
    secondaryPhone = secondaryPhone?.toPhone(),
    billingAddress = billingAddress?.toAddress(LocumAddressType.MAILING.value),
    shippingAddress = shippingAddress?.toAddress(LocumAddressType.STREET.value),
    smsOptIn = smsOptIn,
    // Can remain hardcoded
    securityQuestionAnswer = null,
    isConverted = true,
)

@Suppress("MagicNumber")
fun formatPhoneNumber(input: String): String =
    if (input.length == 10) {
        "${input.substring(0, 3)}-${input.substring(3, 6)}-${input.substring(6)}"
    } else {
        input // Handle cases where the input is not exactly 10 characters
    }

private fun PoetAccountPhone.toPhone() = Phone(1, formatPhoneNumber(phone))

private fun PoetAccountAddress.toAddress(addressType: String) =
    CustomerAddress(
        id = 1,
        addressType = addressType,
        address1 = street,
        address2 = "", // TODO: daas not currently returning address line 2
        city = city,
        state = state,
        postalCode = zipCode,
        regionCode = countryCode,
    )

fun UpdateSecondaryPhoneRequest.toUpdateAccountRequest() =
    UpdateAccountRequest(secondaryPhoneNumber = phone.number?.replace("-", ""))

fun UpdatePrimaryPhoneRequest.toUpdateAccountRequest() =
    UpdateAccountRequest(primaryPhoneNumber = phone.number?.replace("-", ""))

fun UpdateBillingAddressRequest.toUpdateAccountRequest() =
    UpdateAccountRequest(
        billingAddress = address.toPoetAccountAddress(),
    )

fun UpdateShippingAddressRequest.toUpdateAccountRequest() =
    UpdateAccountRequest(
        shippingAddress = address.toPoetAccountAddress(),
    )

fun UpdateSmsOptInRequest.toUpdateAccountRequest() = UpdateAccountRequest(smsOptIn = newSmsOptIn)

fun SameServiceAreaRequest.toPoetRequest(): PoetServiceabilityRequest {
    return PoetServiceabilityRequest(
        originZip = originalAddress.postalCode,
        destinationZip = updatedAddress.postalCode
    )
}
