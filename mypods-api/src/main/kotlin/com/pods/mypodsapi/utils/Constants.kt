package com.pods.mypodsapi.utils

object Constants {
    object Documents {
        val DOCUMENT_TITLES_RETRIEVED_BY_COMPANY_CODE: List<String> =
            listOf(
                "Local Rental Agreement",
                "Local Rental Agreement - Electronic Acceptance",
                "IF Rental Agreement",
                "IF Rental Agreement - Electronic Acceptance",
                "CPO-COO Addendum",
                "CPO-COO Addendum - Electronic Acceptance",
            )
    }

    const val APP_URL = "my.pods.com"
    val EMAIL_REGEX =
        Regex(
            "^(?:[^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*|\".+\")@(\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}]|([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,})\$|^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.^[a-z]{2,3})+\$",
        )

    object Fake {
        const val TWO = 2L
        const val THREE = 3L
        const val FIVE = 5L
    }
}
