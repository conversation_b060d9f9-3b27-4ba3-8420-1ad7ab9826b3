package com.pods.mypodsapi.utils

import datadog.trace.api.interceptor.MutableSpan
import io.opentracing.util.GlobalTracer
import org.slf4j.MDC

object LogAttributes {
    private const val CORRELATION_ID_KEY = "correlation_id"
    private const val REQUEST_PAYLOAD_KEY = "request_payload"
    private const val RESPONSE_PAYLOAD_KEY = "response_payload"
    private const val VISITOR_ID_KEY = "unique_visitor_id"
    private const val CUSTOMER_ID_KEY = "customer_id"
    private const val CARD_VALIDATION_ERROR_KEY = "card_validation_error"
    private const val CARD_VALIDATION_VERBOSE_ERROR_KEY = "card_validation_error_details"

    fun getAllAttributes(): Map<String, String> = MDC.getCopyOfContextMap() ?: emptyMap()
    fun setAttributes(attributes: Map<String, String>) = MDC.setContextMap(attributes)

    fun getCorrelationId(): String? = MDC.get(CORRELATION_ID_KEY)
    fun setCorrelationId(correlationId: String) {
        setTagForRootSpan(CORRELATION_ID_KEY, correlationId)
        MDC.put(CORRELATION_ID_KEY, correlationId)
    }

    fun getVisitorId(): String? = MDC.get(VISITOR_ID_KEY)
    fun setVisitorId(visitorId: String) {
        setTagForRootSpan(VISITOR_ID_KEY, visitorId)
        MDC.put(VISITOR_ID_KEY, visitorId)
    }

    fun getCustomerId(): String? = MDC.get(CUSTOMER_ID_KEY)
    fun setCustomerId(customerId: String) {
        setTagForRootSpan(CUSTOMER_ID_KEY, customerId)
        MDC.put(CUSTOMER_ID_KEY, customerId)
    }

    fun setRequestPayload(payload: String) {
        setTagForRootSpan(REQUEST_PAYLOAD_KEY, payload)
    }

    fun setResponsePayload(payload: String) {
        setTagForRootSpan(RESPONSE_PAYLOAD_KEY, payload)
    }

    fun setCardValidationError(responseCode: String?, responseMessage: String?, verboseDescription: String? = null) {
        setTagForRootSpan(CARD_VALIDATION_ERROR_KEY, "$responseCode - $responseMessage")
        if (verboseDescription != null) {
            GlobalTracer.get().activeSpan()?.setTag(CARD_VALIDATION_VERBOSE_ERROR_KEY, verboseDescription)
        }
    }

    fun clear() = MDC.clear()

    private fun setTagForRootSpan(
        key: String,
        value: String
    ) {
        val span = GlobalTracer.get().activeSpan()
        if (span is MutableSpan) {
            val localRootSpan = (span as MutableSpan).localRootSpan
            localRootSpan.setTag(key, value)
        }
    }
}
