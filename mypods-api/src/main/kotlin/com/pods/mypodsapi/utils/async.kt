package com.pods.mypodsapi.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit

fun <T> CoroutineScope.runAsync(block: suspend () -> T): Deferred<T> {
    val attributes = LogAttributes.getAllAttributes()
    return async {
        LogAttributes.setAttributes(attributes)
        block()
    }
}

suspend fun <A, B> Iterable<A>.parallelMap(
    maxThreadCount: Int = 10,
    f: suspend (A) -> B,
): List<Deferred<B>> =
    coroutineScope {
        val semaphore = Semaphore(maxThreadCount)
        val attributes = LogAttributes.getAllAttributes()
        map {
            async {
                semaphore.withPermit {
                    LogAttributes.setAttributes(attributes)
                    f(it)
                }
            }
        }
    }
