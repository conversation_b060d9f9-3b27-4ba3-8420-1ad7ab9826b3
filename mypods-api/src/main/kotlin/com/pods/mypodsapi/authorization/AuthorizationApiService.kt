package com.pods.mypodsapi.authorization

import com.pods.mypodsapi.config.filters.getLogger
import com.pods.mypodsapi.document.FnpsStatus
import com.pods.mypodsapi.document.IDocumentService
import com.pods.mypodsapi.document.getOutstandingFnpsDocuments
import com.pods.mypodsapi.external.featureFlags.FeatureFlagService
import com.pods.mypodsapi.external.locum.mappers.poetToLegacyOrderData
import com.pods.mypodsapi.orders.Order
import com.pods.mypodsapi.orders.OrderApiService
import com.pods.mypodsapi.utils.LogAttributes
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service

private const val EMPTY_ORDER_ID = ""

@Service
class AuthorizationApiService(
    private val orderApiService: OrderApiService,
    private val documentService: IDocumentService,
    private val featureFlagService: FeatureFlagService,
) {
    val logger = getLogger()

    fun authorizeEntryPoint(customerId: String): EntryPointResult =
        runBlocking {
            val visitorId = LogAttributes.getVisitorId()
            if (featureFlagService.poetFacadeEnabled(customerId)) {
                val orders = orderApiService.getCustomerOrders(customerId)
                val orderIds = orders.map { order: Order -> order.orderId }
                // TODO this should come directly from the documentService instead of orderApiService.
                val orderDocuments =
                    orderApiService
                        .getPoetOrderDocumentsForCustomer(customerId)
                        .filter { orderIds.contains(it.orderId) }
                return@runBlocking EntryPointResult(
                    legacyOrderData = orders.poetToLegacyOrderData(orderDocuments),
                    maintenanceModeEnabled = featureFlagService.maintenanceModeEnabled(visitorId),
                    // TODO see comment in getOutstandingFnpsDocuments - this function currently always returns an empty list
                    fnpsData =
                        orderDocuments
                            .getOutstandingFnpsDocuments(customerId),
                    acornFinancingEnabled = featureFlagService.acornFinancingWidgetEnabled(customerId),
                )
            }
            val legacyOrderData = orderApiService.getLegacyOrderData(customerId)

            var fnpsData: List<FnpsStatus> = emptyList()
            if (featureFlagService.fnpsEnabled()) {
                fnpsData = documentService.getFnpsStatuses(customerId, EMPTY_ORDER_ID)
            }

            return@runBlocking EntryPointResult(
                legacyOrderData,
                featureFlagService.maintenanceModeEnabled(visitorId),
                fnpsData,
                featureFlagService.acornFinancingWidgetEnabled(customerId),
            )
        }
}
