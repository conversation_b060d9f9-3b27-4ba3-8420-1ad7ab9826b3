package com.pods.mypodsapi.authorization

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonView
import com.pods.mypodsapi.config.filters.IncludeInLogs
import com.pods.mypodsapi.customers.LegacyOrderData
import com.pods.mypodsapi.document.FnpsStatus

@Suppress("TooManyFunctions")
data class EntryPointResult(
    @JsonIgnore
    val legacyOrderData: List<LegacyOrderData>,
    @JsonView(IncludeInLogs::class)
    val maintenanceModeEnabled: Boolean,
    @JsonView(IncludeInLogs::class)
    val fnpsData: List<FnpsStatus>,
    @JsonView(IncludeInLogs::class)
    val acornFinancingEnabled: Boolean,
) {
    @JsonView(IncludeInLogs::class)
    val outstandingFnpsAgreements: List<FnpsStatus> = fnpsData.filter { !it.isSigned }

    @JsonView(IncludeInLogs::class)
    val outstandingRentalAgreements: List<OutstandingRentalAgreement> =
        legacyOrderData
            .filter { !it.rentalAgreementAccepted }
            .map {
                OutstandingRentalAgreement(
                    it.orderId,
                    it.companyCode,
                    it.identity,
                    it.rentalAgreementDocId,
                    it.getRentalAgreementType(),
                )
            }

    @JsonView(IncludeInLogs::class)
    val outstandingMothAgreements: List<OutstandingMothAgreement> =
        legacyOrderData
            .filter { !it.invasiveMothAgreementAccepted }
            .map { OutstandingMothAgreement(it.orderId) }

    @JsonView(IncludeInLogs::class)
    val hasWeightTickets = legacyOrderData.any { it.emptyWeightTicketId > 0 || it.filledWeightTicketId > 0 }
}

enum class RentalAgreementType {
    LOCAL,
    IF,
}

@Suppress("unused")
enum class UnsignedRentalAgreementIdentity(
    @JsonView(IncludeInLogs::class)
    val documentCode: String,
) {
    IF_UNSIGNED("CONTRIF-E"),
    LOCAL_UNSIGNED("CONTRL-E"),
    DEFAULT_UNSIGNED("CONTRACT"),
}

data class OutstandingRentalAgreement(
    @JsonView(IncludeInLogs::class)
    val orderId: String,
    @JsonView(IncludeInLogs::class)
    val companyCode: String,
    @JsonView(IncludeInLogs::class)
    val identity: String,
    @JsonView(IncludeInLogs::class)
    val id: String? = null,
    @JsonView(IncludeInLogs::class)
    val agreementType: RentalAgreementType,
)

data class OutstandingMothAgreement(
    @JsonView(IncludeInLogs::class)
    val orderId: String,
)
