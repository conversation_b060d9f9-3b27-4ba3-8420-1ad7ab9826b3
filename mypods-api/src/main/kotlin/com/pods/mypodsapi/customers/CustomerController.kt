package com.pods.mypodsapi.customers

import com.pods.mypodsapi.config.filters.ACCESS_TOKEN
import com.pods.mypodsapi.customers.exceptions.ChallengeEmailException
import com.pods.mypodsapi.customers.exceptions.UpdateEmailException
import com.pods.mypodsapi.customers.exceptions.UpdatePasswordException
import com.pods.mypodsapi.customers.exceptions.VerifyChallengeException
import com.pods.mypodsapi.orders.Order
import com.pods.mypodsapi.orders.OrderApiService
import com.pods.mypodsapi.session.AccessTokenClaims
import com.pods.mypodsapi.session.SessionCookieValues
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Profile
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestAttribute
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/customer")
@Profile("!fake")
class CustomerController(
    private val customerApiService: CustomerApiService,
    private val orderApiService: OrderApiService,
) {
    @GetMapping
    fun getCustomer(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
    ): ResponseEntity<Customer> {
        val customer = customerApiService.getCustomer(claims)
        return ResponseEntity.ok(customer)
    }

    @GetMapping("/orders")
    fun getCustomerOrders(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
    ): ResponseEntity<List<Order>> {
        val response = orderApiService.getCustomerOrders(claims.customerId)
        return ResponseEntity.ok(response)
    }

    @Throws(UpdateEmailException::class)
    @PatchMapping("/email")
    fun updateEmail(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody updateRequest: UpdateEmailRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> {
        val cookieValues = SessionCookieValues.from(servletRequest)
        customerApiService.updateEmail(updateRequest, claims, cookieValues)
        return ResponseEntity.ok().body(Unit)
    }

    @Throws(ChallengeEmailException::class)
    @PutMapping("/email/challenge")
    fun emailChallenge(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody updateRequest: ChallengeEmailRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> {
        val cookieValues = SessionCookieValues.from(servletRequest)
        customerApiService.challengeEmail(updateRequest, claims, cookieValues)
        return ResponseEntity.ok().body(Unit)
    }

    @Throws(VerifyChallengeException::class)
    @PostMapping("/email/verify-challenge")
    fun verifyChallenge(
        @RequestBody verifyChallengeRequest: VerifyChallengeRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> {
        val cookieValues = SessionCookieValues.from(servletRequest)
        customerApiService.verifyChallenge(verifyChallengeRequest, cookieValues)
        return ResponseEntity.ok().body(Unit)
    }

    @Throws(UpdateEmailException::class)
    @PostMapping("/email-verification")
    fun verifyEmailChallenge(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody updateRequest: UpdateEmailRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> {
        val cookieValues = SessionCookieValues.from(servletRequest)
        customerApiService.updateEmail(updateRequest, claims, cookieValues)
        return ResponseEntity.ok().body(Unit)
    }

    @Throws(UpdatePasswordException::class)
    @PatchMapping("/change-password")
    fun updatePassword(
        @RequestBody request: UpdatePasswordRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> {
        val cookieValues = SessionCookieValues.from(servletRequest)
        customerApiService.updatePassword(request, cookieValues)
        return ResponseEntity.ok().body(Unit)
    }

    @PatchMapping("/primary-phone")
    fun updatePrimaryPhone(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: UpdatePrimaryPhoneRequest,
    ): ResponseEntity<Unit> {
        customerApiService.updatePrimaryPhone(claims.customerId, request)
        return ResponseEntity.ok().body(Unit)
    }

    @PatchMapping("/secondary-phone")
    fun updateSecondaryPhone(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: UpdateSecondaryPhoneRequest,
    ): ResponseEntity<Unit> {
        customerApiService.updateSecondaryPhone(claims.customerId, request)
        return ResponseEntity.ok().body(Unit)
    }

    @PatchMapping("/billing-address")
    fun updateBillingAddress(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: UpdateBillingAddressRequest,
    ): ResponseEntity<Unit> {
        customerApiService.updateBillingAddress(claims.customerId, request)
        return ResponseEntity.ok().body(Unit)
    }

    @PatchMapping("/shipping-address")
    fun updateShippingAddress(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: UpdateShippingAddressRequest,
    ): ResponseEntity<Unit> {
        customerApiService.updateShippingAddress(claims.customerId, request)
        return ResponseEntity.ok().body(Unit)
    }

    // TODO LEGACY_REFACTOR make return 400
    @PatchMapping("/pin")
    fun updatePin(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: UpdatePinRequest,
    ): ResponseEntity<Unit> {
        customerApiService.updatePin(claims.customerId, request)
        return ResponseEntity.ok().body(Unit)
    }

    @PatchMapping("/sms-opt-in")
    fun updateSmsOptIn(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: UpdateSmsOptInRequest,
    ): ResponseEntity<Unit> {
        customerApiService.updateSmsOptIn(claims.customerId, request)
        return ResponseEntity.ok().body(Unit)
    }

    @GetMapping("/military-info")
    fun getMilitaryInfo(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
    ): ResponseEntity<MilitaryInformation> {
        customerApiService.getMilitaryInfo(customerId = claims.customerId).let {
            return ResponseEntity.ok().body(it)
        }
    }
}
