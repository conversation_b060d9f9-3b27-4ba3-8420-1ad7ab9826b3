package com.pods.mypodsapi.customers.exceptions

import com.pods.mypodsapi.PodsException
import com.pods.mypodsapi.external.customerAccounts.entities.CAUpdateUsernameResponse
import com.pods.mypodsapi.external.customerAccounts.toResponseCode
import org.springframework.http.HttpStatus

enum class UpdateEmailErrorStatus {
    ACCOUNT_UNDER_MAINTENANCE,
    NO_ACCOUNT_FOUND,
    TOKEN_EXPIRED,
    TOKEN_INVALID,
    EMAIL_ALREADY_IN_USE,
    INVALID_EMAIL,
    ERROR,
}

class UpdateEmailException(
    status: UpdateEmailErrorStatus,
    internalMessage: String,
    httpStatus: HttpStatus,
    cause: Throwable? = null
) : PodsException(
    internalMessage = internalMessage,
    httpStatus = httpStatus,
    errorStatus = status.name,
    cause = cause
) {
    companion object {
        fun customerAccountsError(response: CAUpdateUsernameResponse, httpStatus: HttpStatus) =
            UpdateEmailException(
                status = response.responseCode.toResponseCode(),
                internalMessage = "Customer Accounts failed to update email due to: ${response.responseCode}",
                httpStatus = httpStatus
            )

        fun genericError(exception: Throwable) = UpdateEmailException(
            status = UpdateEmailErrorStatus.ERROR,
            internalMessage = exception.message ?: GENERIC_ERROR_MESSAGE,
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
            cause = exception
        )

        fun validationError(email: String?) = UpdateEmailException(
            status = UpdateEmailErrorStatus.INVALID_EMAIL,
            internalMessage = "Email $email was invalid when trying to update",
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        )
    }
}
