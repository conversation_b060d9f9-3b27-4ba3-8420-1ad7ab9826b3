package com.pods.mypodsapi.customers.legacy

import com.pods.mypodsapi.customers.ChallengeEmailRequest
import com.pods.mypodsapi.customers.Customer
import com.pods.mypodsapi.customers.UpdateBillingAddressRequest
import com.pods.mypodsapi.customers.UpdateEmailRequest
import com.pods.mypodsapi.customers.UpdatePasswordRequest
import com.pods.mypodsapi.customers.UpdatePinRequest
import com.pods.mypodsapi.customers.UpdatePrimaryPhoneRequest
import com.pods.mypodsapi.customers.UpdateSecondaryPhoneRequest
import com.pods.mypodsapi.customers.UpdateShippingAddressRequest
import com.pods.mypodsapi.customers.UpdateSmsOptInRequest
import com.pods.mypodsapi.customers.VerifyChallengeRequest
import com.pods.mypodsapi.customers.exceptions.ChallengeEmailErrorStatus
import com.pods.mypodsapi.customers.exceptions.ChallengeEmailException
import com.pods.mypodsapi.podsready.PodsReadySessionClaims
import com.pods.mypodsapi.session.AccessTokenClaims
import com.pods.mypodsapi.session.ISessionCookieValues
import kotlinx.coroutines.runBlocking
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service

@Service
class LegacyCustomerApiService(private val customersService: LegacyCustomerService) {
    fun getCustomer(
        claims: AccessTokenClaims
    ): Customer =
        runBlocking {
            return@runBlocking customersService.getCustomer(
                claims.customerId,
                claims.converted,
                claims.trackingUuid,
                claims.username
            )
        }

    fun getCustomer(claims: PodsReadySessionClaims): Customer = runBlocking {
        return@runBlocking customersService.getCustomer(
            customerId = claims.customerId,
            isConverted = claims.hasPassword,
            trackingUuid = claims.trackingUuid,
            username = claims.email
        )
    }

    // -- accounts --
    fun updateEmail(
        request: UpdateEmailRequest,
        accessTokenClaims: AccessTokenClaims,
        cookieValues: ISessionCookieValues,
    ) = runBlocking {
        request.validate()
        customersService.updateEmail(request, accessTokenClaims, cookieValues)
    }

    fun challengeEmail(
        request: ChallengeEmailRequest,
        accessTokenClaims: AccessTokenClaims,
        cookieValues: ISessionCookieValues,
    ) = runBlocking {
        request.validate()
        if (accessTokenClaims.username.equals(request.email, true)) {
            throw ChallengeEmailException(
                status = ChallengeEmailErrorStatus.EMAIL_ALREADY_IN_USE,
                "Requested Username matches current username",
                HttpStatus.CONFLICT
            )
        }
        customersService.challengeEmail(request, cookieValues)
    }

    fun verifyChallenge(
        request: VerifyChallengeRequest,
        cookieValues: ISessionCookieValues,
    ) = runBlocking {
        customersService.verifyChallenge(request, cookieValues)
    }

    fun updatePassword(
        request: UpdatePasswordRequest,
        cookieValues: ISessionCookieValues,
    ) = runBlocking {
        request.validate()
        return@runBlocking customersService.updatePassword(request, cookieValues)
    }

    fun updatePin(
        customerId: String,
        request: UpdatePinRequest,
    ) = runBlocking {
        request.validate()
        customersService.updatePin(customerId, request)
    }

    fun updateSmsOptIn(
        customerId: String,
        request: UpdateSmsOptInRequest,
    ) = runBlocking {
        customersService.updateSmsOptIn(customerId, request)
    }

    fun updatePrimaryPhone(
        customerId: String,
        request: UpdatePrimaryPhoneRequest,
    ) = runBlocking {
        request.validate()
        return@runBlocking customersService.updatePrimaryPhone(customerId, request)
    }

    fun updateSecondaryPhone(
        customerId: String,
        request: UpdateSecondaryPhoneRequest,
    ) = runBlocking {
        request.validate()
        return@runBlocking customersService.updateSecondaryPhone(customerId, request)
    }

    fun updateBillingAddress(
        customerId: String,
        request: UpdateBillingAddressRequest,
    ) = runBlocking {
        request.validate()
        return@runBlocking customersService.updateBillingAddress(customerId, request)
    }

    fun updateShippingAddress(
        customerId: String,
        request: UpdateShippingAddressRequest,
    ) = runBlocking {
        request.validate()
        return@runBlocking customersService.updateShippingAddress(customerId, request)
    }
}
