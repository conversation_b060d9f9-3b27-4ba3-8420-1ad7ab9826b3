package com.pods.mypodsapi.fake.availability

import com.pods.mypodsapi.config.filters.ACCESS_TOKEN
import com.pods.mypodsapi.containeravailability.ContainerAvailabilityRequest
import com.pods.mypodsapi.containeravailability.ContainerAvailabilityResponse
import com.pods.mypodsapi.session.AccessTokenClaims
import org.springframework.context.annotation.Profile
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestAttribute
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/availability")
@Profile("fake")
class FakeAvailabilityController {
    private val fakeAvailabilityFactory = FakeAvailabilityFactory()

    @PostMapping("/container-availability")
    @Suppress("unused")
    fun getContainerAvailability(
        @RequestBody request: ContainerAvailabilityRequest,
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
    ): ResponseEntity<ContainerAvailabilityResponse> {
        val response = fakeAvailabilityFactory.createFakeContainerAvailability(claims.customerId)
        return ResponseEntity.ok(response)
    }
}
