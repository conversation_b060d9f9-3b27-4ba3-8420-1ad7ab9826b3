package com.pods.mypodsapi.fake.availability

import com.pods.mypodsapi.containeravailability.ContainerAvailability
import com.pods.mypodsapi.containeravailability.ContainerAvailabilityResponse
import com.pods.mypodsapi.utils.Constants.Fake.THREE
import java.time.LocalDate

class FakeAvailabilityFactory {
    fun createFakeContainerAvailability(customerId: String): ContainerAvailabilityResponse {
        return when (customerId) {
            "111222333" -> createContainerAvailabilityResponse()
            else -> ContainerAvailabilityResponse(emptyList(), emptyList(), listOf(createContainerAvailability()))
        }
        return createContainerAvailabilityResponse()
    }
}

fun createContainerAvailabilityResponse(
    eightFootAvailabilities: List<ContainerAvailability> = listOf(createContainerAvailability()),
    twelveFootAvailabilities: List<ContainerAvailability> = listOf(createContainerAvailability()),
    sixteenFootAvailabilities: List<ContainerAvailability> = listOf(createContainerAvailability()),
) = ContainerAvailabilityResponse(
    eightFootAvailability = eightFootAvailabilities,
    twelveFootAvailability = twelveFootAvailabilities,
    sixteenFootAvailability = sixteenFootAvailabilities,
)

fun createContainerAvailability(
    date: LocalDate = LocalDate.now().plusDays(THREE),
    isAvailable: Boolean = true,
) = ContainerAvailability(
    date = date,
    isAvailable = isAvailable,
)
