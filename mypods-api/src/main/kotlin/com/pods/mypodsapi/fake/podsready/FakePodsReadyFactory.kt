package com.pods.mypodsapi.fake.podsready

import com.pods.mypodsapi.customers.Customer
import com.pods.mypodsapi.external.customerAccounts.entities.CAAccountCreationResponseCode
import com.pods.mypodsapi.external.customerAccounts.entities.CAPodsReadyCreateAccountResponse
import com.pods.mypodsapi.orders.Order
import com.pods.mypodsapi.podsready.AccountCreationStatus
import com.pods.mypodsapi.podsready.PodsReadyCookieFactoryLocal
import com.pods.mypodsapi.podsready.PodsReadyCreateAccountResponse
import com.pods.mypodsapi.podsready.PodsReadyEmailClaims
import com.pods.mypodsapi.podsready.PodsReadyEmailTokenJwtFactory
import com.pods.mypodsapi.podsready.PodsReadySessionClaims
import com.pods.mypodsapi.podsready.PodsReadySessionTokenJwtFactory
import com.pods.mypodsapi.podsready.PodsResponseEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity

@SuppressWarnings("unused")
class FakePodsReadyFactory(
    private val podsReadySessionTokenJwtFactory: PodsReadySessionTokenJwtFactory,
) {
    private val podsReadyCookieFactory = PodsReadyCookieFactoryLocal()

    fun decodeFakePodsReadyToken(token: String): PodsReadyEmailClaims = decodePodsReadyToken(token)

    fun createFakePodsReadySessionClaims(customer: Customer, hasPassword: Boolean = false) =
        createPodsReadySessionClaims(customer, hasPassword)

    fun createFakePodsReadySession(customer: Customer, hasPassword: Boolean = false): PodsResponseEntity<PodsReadySessionClaims> {
        val claims = createPodsReadySessionClaims(customer, hasPassword)
        val token = podsReadySessionTokenJwtFactory.createToken(claims)
        val headers = HttpHeaders()
        headers.add(HttpHeaders.SET_COOKIE, podsReadyCookieFactory.createCookie(token))
        return PodsResponseEntity(
            data = claims,
            httpStatus = HttpStatus.OK,
            headers = headers
        )
    }

    fun createFakeMyPodsCreateAccountResponse(
        accountCreationStatus: AccountCreationStatus,
        httpHeaders: HttpHeaders
    ) =
        createMyPodsCreateAccountResponse(accountCreationStatus, httpHeaders)

    fun getFakePodsReadyOrdersResponse(orders: List<Order>, httpStatus: HttpStatus) =
        createOrdersResponse(orders, httpStatus)
}

var podsReadyEmailTokenJwtFactory = PodsReadyEmailTokenJwtFactory(
    marketingCloudSigningKey = "XP27mePuoIkib903OAlnrqvxS9VFjMGw"
)

fun decodePodsReadyToken(token: String): PodsReadyEmailClaims =
    podsReadyEmailTokenJwtFactory.decodeToken(token)

fun createPodsReadySessionClaims(customer: Customer, hasPassword: Boolean = false) =
    PodsReadySessionClaims(
        customerId = customer.id,
        email = customer.email?.address ?: "<EMAIL>",
        hasPassword = hasPassword,
        type = customer.customerType,
        firstName = customer.firstName,
        lastName = customer.lastName,
        militaryBranch = "unknown",
        militaryStatus = "unknown",
    )

fun createCAPodsReadyCreateAccountResponse(
    responseCode: CAAccountCreationResponseCode = CAAccountCreationResponseCode.SUCCESS
) = CAPodsReadyCreateAccountResponse(
    accountCreationStatus = responseCode,
)

fun createMyPodsCreateAccountResponse(
    accountCreationStatus: AccountCreationStatus = AccountCreationStatus.SUCCESS,
    headers: HttpHeaders = HttpHeaders()
) = PodsReadyCreateAccountResponse(
    accountCreationStatus = accountCreationStatus,
    headers = headers
)

fun createOrdersResponse(
    orders: List<Order> = emptyList(),
    httpStatus: HttpStatus = HttpStatus.OK,
    headers: HttpHeaders = HttpHeaders()
): ResponseEntity<List<Order>> =
    PodsResponseEntity(data = orders, httpStatus = httpStatus, headers = headers).toResponseEntity()
