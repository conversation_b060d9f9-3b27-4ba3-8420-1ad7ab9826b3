package com.pods.mypodsapi.fake.document

import com.pods.mypodsapi.document.CustomerDocument
import com.pods.mypodsapi.document.DocumentType
import com.pods.mypodsapi.document.MonthlyBillingStatement
import java.time.LocalDateTime
import java.time.ZonedDateTime

fun createCustomerDocument(
    docRef: String = "DefRef123.pdf",
    docName: String = "Document Name",
    isCustomerFacing: Boolean = true,
    id: String = "123",
    docType: DocumentType = DocumentType.SETTLEMENT_AGREEMENT,
    tags: String = "Completed",
    docNotes: String = "docNotes",
    title: String = "doc title",
) = CustomerDocument(
    docRef = docRef,
    docName = docName,
    isCustomerFacing = isCustomerFacing,
    id = id,
    docType = docType,
    tags = tags,
    docNotes = docNotes,
    title = title,
)

fun createMonthlyBillingStatement(
    childCustomerAccount: String? = null,
    commercialInvoiceStatementDate: ZonedDateTime? = ZonedDateTime.parse("2024-04-14T00:00:00Z"),
    commercialInvoiceStatementDesc: String? = "2024-4, 3/23/2024 - 4/22/2024",
    commercialInvoiceStatementId: String? = "PODSCS02140457",
    createdBy: String? = "Admin",
    createdDateTime: LocalDateTime? = LocalDateTime.parse("2024-04-23T07:00:18"),
    docuRefIdentity: Long? = **********,
    fileName: String? = null,
    filePathAndName: String? = null,
    fileType: String? = null,
    invoiceType: String? = "Retail",
    parentCustomerAccount: String? = "*********",
    path: String? = null,
) = MonthlyBillingStatement(
    childCustomerAccount = childCustomerAccount,
    commercialInvoiceStatementDate = commercialInvoiceStatementDate,
    commercialInvoiceStatementDesc = commercialInvoiceStatementDesc,
    commercialInvoiceStatementId = commercialInvoiceStatementId,
    createdBy = createdBy,
    createdDateTime = createdDateTime,
    docuRefIdentity = docuRefIdentity,
    fileName = fileName,
    filePathAndName = filePathAndName,
    fileType = fileType,
    invoiceType = invoiceType,
    parentCustomerAccount = parentCustomerAccount,
    path = path,
)
