package com.pods.mypodsapi.fake.podsready

import com.pods.mypodsapi.authorization.EntryPointResult
import com.pods.mypodsapi.config.filters.PodsReadySessionFilter.Companion.PODS_READY_ACCESS_TOKEN
import com.pods.mypodsapi.customers.Customer
import com.pods.mypodsapi.document.AcceptRentalAgreementRequest
import com.pods.mypodsapi.document.SignMothAgreementRequest
import com.pods.mypodsapi.document.pdfHeader
import com.pods.mypodsapi.fake.authorization.FakeEntrypointFactory
import com.pods.mypodsapi.fake.customer.FakeCustomerFactory
import com.pods.mypodsapi.fake.document.FakeDocumentFactory
import com.pods.mypodsapi.fake.order.FakeOrderFactory
import com.pods.mypodsapi.fake.payment.FakePaymentFactory
import com.pods.mypodsapi.orders.Order
import com.pods.mypodsapi.payments.PaymentMethod
import com.pods.mypodsapi.podsready.AccountCreationStatus
import com.pods.mypodsapi.podsready.PodsReadyAccountStatusResponse
import com.pods.mypodsapi.podsready.PodsReadyCreateAccountRequest
import com.pods.mypodsapi.podsready.PodsReadyService.Companion.PODS_READY_COOKIE_NAME
import com.pods.mypodsapi.podsready.PodsReadySessionClaims
import com.pods.mypodsapi.podsready.PodsReadySessionRequest
import com.pods.mypodsapi.podsready.PodsReadySessionTokenJwtFactory
import com.pods.mypodsapi.podsready.PodsResponseEntity
import com.pods.mypodsapi.session.ACCESS_TOKEN_COOKIE_NAME
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Profile
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.CookieValue
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestAttribute
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/legacy/pods-ready")
@Profile("fake")
@SuppressWarnings("unused")
class FakeLegacyPodsReadyController(
    podsReadySessionTokenJwtFactory: PodsReadySessionTokenJwtFactory
) {
    private val fakePodsReadyFactory = FakePodsReadyFactory(podsReadySessionTokenJwtFactory)
    private val fakeOrderFactory = FakeOrderFactory()
    private val fakeCustomerFactory = FakeCustomerFactory()
    private val fakeEntrypointFactory = FakeEntrypointFactory()
    private val fakeDocumentFactory = FakeDocumentFactory()
    private val fakePaymentFactory = FakePaymentFactory()

    @PostMapping("/session")
    fun startOrGetPodsReadySession(
        @RequestBody request: PodsReadySessionRequest,
        @RequestAttribute(PODS_READY_ACCESS_TOKEN) claims: PodsReadySessionClaims?,
        @CookieValue(value = PODS_READY_COOKIE_NAME) podsReadyAccessCookie: String = "",
        @CookieValue(value = ACCESS_TOKEN_COOKIE_NAME) accessTokenCookie: String = "",
    ): ResponseEntity<PodsReadySessionClaims> {
        if (request.token == LEGACY_NOT_PODS_READY) {
            return PodsResponseEntity<PodsReadySessionClaims>(httpStatus = HttpStatus.FORBIDDEN).toResponseEntity()
        }
        val customer = fakeCustomerFactory.createFakeCustomer(customerId = request.token)
        return fakePodsReadyFactory.createFakePodsReadySession(customer).toResponseEntity()
    }

    @PostMapping("/create-account")
    fun createAccount(
        @CookieValue(value = PODS_READY_COOKIE_NAME) podsReadyAccessCookie: String = "",
        @RequestBody request: PodsReadyCreateAccountRequest
    ): ResponseEntity<PodsReadyAccountStatusResponse> {
        val response =
            fakePodsReadyFactory.createFakeMyPodsCreateAccountResponse(AccountCreationStatus.SUCCESS, HttpHeaders())

        return ResponseEntity
            .status(response.accountCreationStatus.httpStatus)
            .headers(response.headers)
            .body(PodsReadyAccountStatusResponse(accountCreationStatus = response.accountCreationStatus))
    }

    @GetMapping("/customer")
    fun getCustomer(
        @RequestAttribute(PODS_READY_ACCESS_TOKEN) claims: PodsReadySessionClaims,
    ): ResponseEntity<Customer> {
        val customer = fakeCustomerFactory.createFakeCustomer(claims)
        return ResponseEntity.ok(customer)
    }

    @GetMapping("/entrypoint")
    fun entryPoint(
        @RequestAttribute(PODS_READY_ACCESS_TOKEN) claims: PodsReadySessionClaims,
    ): ResponseEntity<EntryPointResult> {
        val response = fakeEntrypointFactory.createFakeEntrypointResult(claims.customerId)
        return ResponseEntity.ok(response)
    }

    @GetMapping("/orders")
    fun getPodsReadyOrder(
        @CookieValue(value = PODS_READY_COOKIE_NAME) podsReadyAccessCookie: String = "",
    ): ResponseEntity<List<Order>> {
        val customerId = findCustomerId(podsReadyAccessCookie.toLongOrNull()?.toString())
        val order = fakeOrderFactory.createFakeOrder(customerId)
        return ResponseEntity.ok(listOf(order))
    }

    @GetMapping("/rental-agreement/{companyCode}")
    fun getRentalAgreement(
        @PathVariable companyCode: String,
    ): ResponseEntity<ByteArray> {
        val content = fakeDocumentFactory.createFakeRentalAgreementPdf(companyCode)
        return ResponseEntity(content, pdfHeader("rental_agreement_$companyCode"), HttpStatus.OK)
    }

    @PatchMapping("/accept-rental-agreement")
    fun acceptRentalAgreement(
        @RequestAttribute(PODS_READY_ACCESS_TOKEN) claims: PodsReadySessionClaims,
        @RequestBody request: AcceptRentalAgreementRequest,
        servletRequest: HttpServletRequest,
    ) {
        println("acceptRentalAgreement $claims")
    }

    @PostMapping("/sign-moth-agreement")
    fun signMothAgreement(
        @RequestAttribute(PODS_READY_ACCESS_TOKEN) claims: PodsReadySessionClaims,
        @RequestBody request: SignMothAgreementRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> {
        println("acceptMothAgreement $claims")
        return ResponseEntity.ok().build()
    }

    @GetMapping("/methods")
    fun getPaymentMethods(
        @CookieValue(value = PODS_READY_COOKIE_NAME) podsReadyAccessCookie: String = "",
    ): ResponseEntity<List<PaymentMethod>> {
        val customerId = findCustomerId(podsReadyAccessCookie.toLongOrNull()?.toString())
        val response = fakePaymentFactory.createFakePaymentMethods(customerId)
        return ResponseEntity.ok(response)
    }

    private fun findCustomerId(vararg userIds: String?): String {
        return userIds.find { !it.isNullOrBlank() } ?: LEGACY_SINGLE_TASK_AND_PASSWORD_ON_BOARDING
    }

    companion object {
        // these are set up in split in testing and staging env - you also need to change the uniqueVisitorID cookie to match otherwise we default to poet enabled
        const val LEGACY_NOT_PODS_READY = "legacy_not_pods_ready"
        const val LEGACY_SINGLE_TASK_AND_PASSWORD_ON_BOARDING = "legacy_single_task_and_password_on_boarding"
        const val LEGACY_SINGLE_TASK_ONLY = "legacy_single_task_only"
        const val LEGACY_DOUBLE_TASK_ONLY = "legacy_double_task_only"
        const val LEGACY_DOUBLE_TASK_AND_PASSWORD_ON_BOARDING = "legacy_double_and_password_on_boarding"
        // const val LEGACY_PASSWORD_ON_BOARDING_ONLY = "legacy_password_on_boarding_only"
    }
}
