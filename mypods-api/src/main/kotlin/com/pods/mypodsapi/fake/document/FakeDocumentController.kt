package com.pods.mypodsapi.fake.document

import com.pods.mypodsapi.config.filters.ACCESS_TOKEN
import com.pods.mypodsapi.config.filters.DOCUMENT_TOKEN
import com.pods.mypodsapi.document.AcceptRentalAgreementRequest
import com.pods.mypodsapi.document.CustomStatementRequest
import com.pods.mypodsapi.document.DocumentsResponse
import com.pods.mypodsapi.document.SignFnpsRequest
import com.pods.mypodsapi.document.SignMothAgreementRequest
import com.pods.mypodsapi.document.tokens.DocumentTokenClaims
import com.pods.mypodsapi.session.AccessTokenClaims
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Profile
import org.springframework.http.ContentDisposition
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestAttribute
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/document")
@Profile("fake")
@SuppressWarnings("unused")
class FakeDocumentController {
    private val fakeDocumentFactory = FakeDocumentFactory()

    @GetMapping("/{documentId}")
    fun getFile(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @PathVariable documentId: String,
    ): ResponseEntity<ByteArray> {
        val content = fakeDocumentFactory.createFakeDocumentPdf(documentId)
        return ResponseEntity(content, pdfHeader("document_$documentId"), HttpStatus.OK)
    }

    @GetMapping("/sas-url", produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getBlobUrlWithSasToken(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestParam docRef: String,
    ): ResponseEntity<String> {
        val fakeUrl = fakeDocumentFactory.createFakeSasUrl(docRef)
        return ResponseEntity(fakeUrl, HttpStatus.OK)
    }

    @GetMapping("/documents")
    fun getDocuments(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestAttribute(DOCUMENT_TOKEN) documentClaims: DocumentTokenClaims?,
    ): ResponseEntity<DocumentsResponse> {
        val documentsResponse = fakeDocumentFactory.createFakeDocumentsResponse(claims.customerId)
        return ResponseEntity.ok(documentsResponse)
    }

    @PatchMapping("/accept-rental-agreement")
    fun acceptRentalAgreement(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: AcceptRentalAgreementRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> = ResponseEntity.ok().build()

    @GetMapping("/rental-agreement/{companyCode}")
    fun getRentalAgreement(
        @PathVariable companyCode: String,
    ): ResponseEntity<ByteArray> {
        val content = fakeDocumentFactory.createFakeRentalAgreementPdf(companyCode)
        return ResponseEntity(content, pdfHeader("rental_agreement_$companyCode"), HttpStatus.OK)
    }

    @PostMapping("/custom-statement")
    fun generateCustomStatement(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: CustomStatementRequest,
    ): ResponseEntity<ByteArray> {
        val content = fakeDocumentFactory.createFakeCustomStatementPdf(request.startDate)
        return ResponseEntity(content, pdfHeader("custom_statement"), HttpStatus.OK)
    }

    @PostMapping("/sign-fnps")
    fun signFnpsPdf(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: SignFnpsRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> = ResponseEntity.ok(Unit)

    @PostMapping("/sign-moth-agreement")
    fun signMothAgreement(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: SignMothAgreementRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> = ResponseEntity.ok().build()

    private fun pdfHeader(fileName: String = "file") =
        HttpHeaders().apply {
            contentType = MediaType.APPLICATION_PDF
            contentDisposition = ContentDisposition.inline().filename("$fileName.pdf").build()
        }
}
