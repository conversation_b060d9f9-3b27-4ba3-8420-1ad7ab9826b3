package com.pods.mypodsapi.fake.payment

import com.pods.mypodsapi.config.filters.ACCESS_TOKEN
import com.pods.mypodsapi.customers.exceptions.PayInvoiceCreditCardException
import com.pods.mypodsapi.customers.exceptions.PayInvoiceException
import com.pods.mypodsapi.payments.AddPaymentMethodRequest
import com.pods.mypodsapi.payments.PayInvoicesRequest
import com.pods.mypodsapi.payments.PaymentMethod
import com.pods.mypodsapi.payments.SetDefaultPaymentMethodRequest
import com.pods.mypodsapi.session.AccessTokenClaims
import org.springframework.context.annotation.Profile
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestAttribute
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/payment")
@Profile("fake")
@SuppressWarnings("unused")
class FakePaymentController {
    private val fakePaymentFactory = FakePaymentFactory()

    @GetMapping("/methods")
    fun getPaymentMethods(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
    ): ResponseEntity<List<PaymentMethod>> {
        val response = fakePaymentFactory.createFakePaymentMethods(claims.customerId)
        return ResponseEntity.ok(response)
    }

    @PatchMapping("/set-default-payment-method")
    fun setDefaultPaymentMethod(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: SetDefaultPaymentMethodRequest,
    ): ResponseEntity<Unit> = ResponseEntity.ok().body(Unit)

    @PostMapping("/payment-method")
    fun addPaymentMethod(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: AddPaymentMethodRequest,
    ): ResponseEntity<Unit> = ResponseEntity.ok().body(Unit)

    @PostMapping("/pay-invoices")
    @Throws(PayInvoiceException::class, PayInvoiceCreditCardException::class)
    fun payInvoices(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: PayInvoicesRequest,
    ): ResponseEntity<Unit> {
        fakePaymentFactory.makeFakePayment(request.totalPaymentAmount)
        return ResponseEntity.ok().body(Unit)
    }
}
