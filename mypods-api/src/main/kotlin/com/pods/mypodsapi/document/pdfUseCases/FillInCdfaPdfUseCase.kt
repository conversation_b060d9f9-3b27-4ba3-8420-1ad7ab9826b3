package com.pods.mypodsapi.document.pdfUseCases

import com.lowagie.text.pdf.BaseFont
import com.lowagie.text.pdf.PdfReader
import com.lowagie.text.pdf.PdfStamper
import com.pods.mypodsapi.debug.usecases.MothCheckboxId
import com.pods.mypodsapi.debug.usecases.MothOtherCheckbox
import org.springframework.stereotype.Component
import java.awt.Color
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale

data class FillInCdfaPdfRequest(
    val firstName: String,
    val lastName: String,
    val address: String,
    val email: String,
    val checkboxesToSelect: List<MothCheckboxId>,
    val otherCheckboxes: List<MothOtherCheckbox>,
    val dateSigned: ZonedDateTime
) {
    val fullName: String = "$firstName $lastName"
}

@Component
class FillInCdfaPdfUseCase(
    lanternFlyMothCsv: LanternFlyMothCsv,
) {
    private val blankPdf = this::class.java.getResourceAsStream("/pdfs/cdfa.pdf")!!.readAllBytes()
    private val normalFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.WINANSI, BaseFont.EMBEDDED)

    private val rows = lanternFlyMothCsv.rows.associateBy { it.id }

    fun invoke(request: FillInCdfaPdfRequest): ByteArray {
        val output = fillInPDF(request, blankPdf)
        return output
    }

    private fun fillInPDF(
        request: FillInCdfaPdfRequest,
        templatePdf: ByteArray,
    ): ByteArray {
        val reader = PdfReader(ByteArrayInputStream(templatePdf))
        val baos = ByteArrayOutputStream()
        val stamper = PdfStamper(reader, baos)

        fillInCheckboxes(request.checkboxesToSelect, stamper)
        fillInOtherTextbox(request.otherCheckboxes, stamper)
        fillInSignature(request, stamper)

        stamper.setFormFlattening(true)
        stamper.close()
        reader.close()

        return baos.toByteArray()
    }

    private fun fillInCheckboxes(
        checkboxesToSelect: List<MothCheckboxId>,
        stamper: PdfStamper,
    ) {
        checkboxesToSelect.forEach {
            val cdfaPdfCheckboxName = rows[it]?.cdfaCheckboxName
            if (cdfaPdfCheckboxName != null) {
                stamper.acroFields.setField(cdfaPdfCheckboxName, "Yes")
            }
        }
    }

    private fun fillInOtherTextbox(otherCheckboxes: List<MothOtherCheckbox>, stamper: PdfStamper,) {
        val combineOtherText = otherCheckboxes
            .mapNotNull { it.description?.trim() }
            .filter { it.isNotEmpty() }
            .joinToString(", ")
        if (combineOtherText.isNotEmpty()) {
            stamper.acroFields.setField("Other", "Yes")
            stamper.acroFields.setField("Other_text", combineOtherText)
        }
    }

    private fun fillInSignature(
        request: FillInCdfaPdfRequest,
        stamper: PdfStamper,
    ) {
        val content = stamper.getOverContent(1) // Get the content of the first page
        content.beginText()
        // Name
        stamper.acroFields.setField("Signature_Name", request.fullName)

        // Signature
        stamper.acroFields.setField("Signature_Signature", request.fullName)

        // Address
        content.setFontAndSize(normalFont, 10f)
        content.setColorFill(Color(0x08, 0x35, 0x44))
        content.setTextMatrix(90F, 104F)
        content.showText(request.address)

        // Email
        content.setFontAndSize(normalFont, 10f)
        content.setColorFill(Color(0x08, 0x35, 0x44))
        content.setTextMatrix(120F, 70F)
        content.showText(request.email)

        // Date Signed
        content.setFontAndSize(normalFont, 10f)
        content.setColorFill(Color(0x08, 0x35, 0x44))
        content.setTextMatrix(405F, 70F)
        content.showText(request.dateSigned.format(DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH)))
        content.endText()
    }
}
