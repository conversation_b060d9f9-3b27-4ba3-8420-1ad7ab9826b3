package com.pods.mypodsapi.document

import com.pods.mypodsapi.config.filters.ACCESS_TOKEN
import com.pods.mypodsapi.config.filters.DOCUMENT_TOKEN
import com.pods.mypodsapi.document.tokens.DocumentTokenClaims
import com.pods.mypodsapi.session.AccessTokenClaims
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Profile
import org.springframework.http.ContentDisposition
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestAttribute
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/document")
@Profile("!fake")
class DocumentController(
    private val documentApiService: DocumentApiService,
) {
    @GetMapping("/{documentId}")
    fun getFile(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestAttribute(DOCUMENT_TOKEN) documentClaims: DocumentTokenClaims,
        @PathVariable documentId: String,
    ): ResponseEntity<ByteArray> {
        val bytes = documentApiService.getFileForDocumentId(documentId, claims.customerId, documentClaims)
        return ResponseEntity(bytes, pdfHeader(), HttpStatus.OK)
    }

    @GetMapping("/sas-url", produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getBlobUrlWithSasToken(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestParam docRef: String,
    ): ResponseEntity<String> {
        val url = documentApiService.getBlobUrlWithSasToken(docRef, claims.customerId)
        return ResponseEntity(url, HttpStatus.OK)
    }

    @GetMapping("/documents")
    fun getDocuments(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestAttribute(DOCUMENT_TOKEN) documentClaims: DocumentTokenClaims?,
    ): ResponseEntity<DocumentsResponse> {
        val documentsResponse = documentApiService.getDocuments(claims.customerId, documentClaims)
        return ResponseEntity.ok(documentsResponse)
    }

    @PatchMapping("/accept-rental-agreement")
    fun acceptRentalAgreement(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: AcceptRentalAgreementRequest,
        servletRequest: HttpServletRequest,
    ) {
        documentApiService.acceptRentalAgreement(claims.customerId, request, getClientIpFor(servletRequest))
    }

    @GetMapping("/rental-agreement/{companyCode}")
    fun getRentalAgreement(
        @PathVariable companyCode: String,
    ): ResponseEntity<ByteArray> {
        val bytes = documentApiService.getRentalAgreement(companyCode)
        return ResponseEntity(bytes, pdfHeader(), HttpStatus.OK)
    }

    @PostMapping("/custom-statement")
    fun generateCustomStatement(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: CustomStatementRequest,
    ): ResponseEntity<ByteArray> {
        val bytes = documentApiService.generateCustomStatement(request, claims.customerId)
        return ResponseEntity(bytes, pdfHeader(), HttpStatus.OK)
    }

    @PostMapping("/sign-fnps")
    fun signFnpsPdf(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: SignFnpsRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> {
        documentApiService.signFnpsPdf(claims.customerId, request, getClientIpFor(servletRequest))
        return ResponseEntity.ok(Unit)
    }

    @PostMapping("/sign-moth-agreement")
    fun signMothAgreement(
        @RequestAttribute(ACCESS_TOKEN) claims: AccessTokenClaims,
        @RequestBody request: SignMothAgreementRequest,
        servletRequest: HttpServletRequest,
    ): ResponseEntity<Unit> {
        documentApiService.signMothAgreement(claims.customerId, getClientIpFor(servletRequest), request)
        return ResponseEntity.ok().build()
    }
}

private fun getClientIpFor(request: HttpServletRequest): String {
    val headersToCheck = listOf("X-Forwarded-For", "X-Real-IP", "X-Client-IP")

    for (header in headersToCheck) {
        val ip = request.getHeader(header)
        if (ip != null && ip.isNotEmpty()) {
            return ip.split(",")[0].trim() // Return the first IP in the list
        }
    }

    return request.remoteAddr // Fallback to remoteAddr if no headers are found
}

fun pdfHeader(fileName: String = "file") =
    HttpHeaders().apply {
        contentType = MediaType.APPLICATION_PDF
        contentDisposition = ContentDisposition.inline().filename("$fileName.pdf").build()
    }
