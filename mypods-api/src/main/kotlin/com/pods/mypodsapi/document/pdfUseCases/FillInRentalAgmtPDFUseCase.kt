package com.pods.mypodsapi.document.pdfUseCases

import com.lowagie.text.pdf.PdfReader
import com.lowagie.text.pdf.PdfStamper
import com.pods.mypodsapi.document.AcceptRentalAgreementRequest
import org.springframework.stereotype.Component
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@Component
class FillInRentalAgmtPDFUseCase {

    fun invoke(
        request: AcceptRentalAgreementRequest,
        customerId: String,
    ): ByteArray {
        val templatePdf = this::class.java.getResourceAsStream(request.filePath!!)!!.readAllBytes()
        val reader = PdfReader(ByteArrayInputStream(templatePdf))
        val baos = ByteArrayOutputStream()
        val stamper = PdfStamper(reader, baos)

        if (request.companyCode == "PEIU") {
            fillInCorporateFormFields(stamper, request, customerId)
        } else {
            fillInUSFranchiseFormFields(stamper, request)
        }

        stamper.setFormFlattening(true)
        stamper.close()
        reader.close()

        return baos.toByteArray()
    }

    private fun fillInCorporateFormFields(
        stamper: PdfStamper,
        request: AcceptRentalAgreementRequest,
        customerId: String,
    ) {

        val content = stamper.getOverContent(13)
        content.beginText()

        stamper.acroFields.setField(
            "signature-name",
            request.fullName,
        )

        stamper.acroFields.setField(
            "signature-signature",
            request.fullName,
        )

        stamper.acroFields.setField(
            "signature-cuid",
            customerId,
        )

        val dateSigned = formattedDate(request.dateSigned)
        stamper.acroFields.setField("signature-date", dateSigned)

        content.endText()
    }

    private fun fillInUSFranchiseFormFields(
        stamper: PdfStamper,
        request: AcceptRentalAgreementRequest
    ) {

        val content = stamper.getOverContent(1) // Get the content of the first page
        content.beginText()

        stamper.acroFields.setField(
            "signature-name",
            request.fullName,
        )

        stamper.acroFields.setField(
            "signature-signature",
            request.fullName,
        )

        val formattedDate = request.dateSigned.format(DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH))
        stamper.acroFields.setField("signature-date", formattedDate)

        content.endText()
    }

    private fun formattedDate(date: ZonedDateTime): String {
        return date.format(DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH))
    }
}
