package com.pods.mypodsapi.billing.legacy

import com.pods.mypodsapi.billing.BillingInformation
import com.pods.mypodsapi.billing.BillingResponse
import com.pods.mypodsapi.document.tokens.DocumentJWTFactory
import com.pods.mypodsapi.document.tokens.DocumentTokenClaims
import com.pods.mypodsapi.external.adapter.legacy.LegacyDocumentAdapter
import com.pods.mypodsapi.utils.runAsync
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service

@Service
class LegacyBillingApiService(
    private val documentService: LegacyDocumentAdapter,
    private val documentJWTFactory: DocumentJWTFactory,
) {
    fun getBillingInformation(
        customerId: String,
        documentClaims: DocumentTokenClaims?,
    ): BillingResponse =

        runBlocking {
            val documentIds = documentClaims?.getAuthorizedDocumentIds(customerId) ?: emptySet()

            val deferredInvoices = runAsync { documentService.getInvoices(customerId) }
            val deferredMonthlyStatements = runAsync { documentService.getMonthlyStatements(customerId) }
            val invoices = deferredInvoices.await()
            val monthlyStatements = deferredMonthlyStatements.await()
            val ownedBillingIds =
                invoices.map { it.documentId } +
                        monthlyStatements.map { it.docuRefIdentity.toString() }

            val documentTokenClaims =
                DocumentTokenClaims(
                    customerId = customerId,
                    allDocumentIds = documentIds.plus(ownedBillingIds),
                )

            val token = documentJWTFactory.createDocumentToken(documentTokenClaims)
            val billingInformation = BillingInformation(invoices, monthlyStatements)

            return@runBlocking BillingResponse(billingInformation, token)
        }
}
