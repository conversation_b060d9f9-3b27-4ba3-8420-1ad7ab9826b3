package com.pods.mypodsapi.containeravailability

import com.pods.mypodsapi.external.containeravailability.AvailabilityClient
import com.pods.mypodsapi.external.containeravailability.toContainerAvailabilityResponse
import com.pods.mypodsapi.external.containeravailability.toPodsRequest
import com.pods.mypodsapi.external.containeravailability.toPoetRequest
import com.pods.mypodsapi.external.featureFlags.FeatureFlagService
import com.pods.mypodsapi.external.poetFacade.PoetFacadeClient
import kotlinx.coroutines.reactive.awaitFirstOrNull
import org.springframework.stereotype.Service

@Service
class AvailabilityService(
    private val availabilityClient: AvailabilityClient,
    private val poetClient: PoetFacadeClient,
    private val featureFlagService: FeatureFlagService,
) : IAvailabilityService {
    override suspend fun getContainerAvailability(
        request: ContainerAvailabilityRequest,
        customerId: String,
    ): ContainerAvailabilityResponse {
        if (featureFlagService.poetFacadeEnabled(customerId)) {
            return poetClient
                .getContainerAvailability(request.toPoetRequest())
                .awaitFirstOrNull()!!
                .toContainerAvailabilityResponse()
        }
        return availabilityClient
            .getContainerAvailability(request.toPodsRequest())
            .awaitFirstOrNull()!!
            .toContainerAvailabilityResponse()
    }
}
