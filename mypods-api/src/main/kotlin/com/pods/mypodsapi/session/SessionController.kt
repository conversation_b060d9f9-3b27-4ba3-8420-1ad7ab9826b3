package com.pods.mypodsapi.session

import com.pods.mypodsapi.config.filters.getLogger
import com.pods.mypodsapi.utils.LogAttributes
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Profile
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/session")
@Profile("!fake")
class SessionController(
    private val sessionService: SessionApiService, private val sessionTokenJwtFactory: SessionTokenJwtFactory
) {
    val logger = getLogger()

    @PostMapping("/refresh")
    @Throws(AuthorizationException::class)
    fun refreshSession(servletRequest: HttpServletRequest): ResponseEntity<Any> {
        // Expected to simply return a 500 if session is unable to be refreshed
        try {
            val sessionCookies = SessionCookieValues.from(servletRequest)
            tryAddClaimsToLogAttributes(sessionCookies)
            val refreshedSession = sessionService.getRefreshedSession(sessionCookies)
            return ResponseEntity(refreshedSession.accessTokenClaims, refreshedSession.newSession.httpHeaders, HttpStatus.OK)
        } catch (exception: AuthorizationException) {
            val message = "Failed to refresh session"
            logger.error(message, exception)
            // Do not return UNEXPECTED_AUTH_FAILURE so front end does not automatically logout user
            if (exception.status == AuthorizationErrorStatus.UNEXPECTED_AUTH_FAILURE) {
                return ResponseEntity.internalServerError().body(message)
            }

            throw exception
        }
    }

    private fun tryAddClaimsToLogAttributes(sessionCookies: SessionCookieValues) {
        try {
            val claims = sessionTokenJwtFactory.decodeAccessToken(sessionCookies.accessTokenCookie!!.value)
            LogAttributes.setCustomerId(claims.customerId)
        } catch (_: Exception) {
        }
    }
}
