{"CallInfo": {"StatusCode": "OK", "StatusMessage": "OK", "ServerUsed": "TPASTVWIWEB04", "ExceptionDetails": null}, "Response": [{"OrderId": "4865600", "OrderType": "Local", "QuoteId": "*********", "RentalAgreementAccepted": true, "InvasiveMothAgreementAccepted": true, "OrderDate": "2024-05-16T15:46:10.5458736", "QuoteChannelType": "Web", "Price": 0.0, "Containers": [{"ContainerId": null, "ContainerType": "12-foot length container", "CompanyCode": "Z101", "HasCityServiceMoves": false, "MoveLegs": [{"MoveLegId": "*********P1S1", "MoveLegType": "NEW", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2024-07-08T00:00:00", "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "13133 34th St N", "Address2": "201", "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "SchedulingRule": {"CanSchedule": true, "ScheduleStart": "2024-06-04T17:22:15.969984-04:00", "ScheduleEnd": "2024-07-15T00:00:00"}}, {"MoveLegId": "*********P1S2", "MoveLegType": "WRT", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": null, "ETA": null, "TransitDays": 1, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "13133 34th St N", "Address2": "201", "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P1S3", "MoveLegType": "STG", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": null, "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd ", "Address2": "Ste 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd ", "Address2": "Ste 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P2S3", "MoveLegType": "WTW", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": null, "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd ", "Address2": "Ste 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd ", "Address2": "Ste 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P1S5", "MoveLegType": "RDL", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": null, "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": null, "Address2": null, "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P1S6", "MoveLegType": "FPU", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": null, "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": null, "Address2": null, "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}]}, {"ContainerId": null, "ContainerType": "16-foot length container", "CompanyCode": "Z101", "HasCityServiceMoves": false, "MoveLegs": [{"MoveLegId": "*********P2S1", "MoveLegType": "NEW", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2023-07-08T00:00:00", "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "13133 34th St N", "Address2": "201", "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "SchedulingRule": {"CanSchedule": true, "ScheduleStart": "2024-06-04T17:22:15.969984-04:00", "ScheduleEnd": "2024-07-15T00:00:00"}}, {"MoveLegId": "*********P2S2", "MoveLegType": "WRT", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2023-09-08T00:00:00", "ETA": null, "TransitDays": 1, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "13133 34th St N", "Address2": "201", "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P2S3", "MoveLegType": "STG", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2023-09-29T00:00:00", "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd ", "Address2": "Ste 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd ", "Address2": "Ste 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P3S3", "MoveLegType": "WTW", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2023-10-08T00:00:00", "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd ", "Address2": "Ste 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd ", "Address2": "Ste 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P2S5", "MoveLegType": "RDL", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2024-06-30T00:00:00", "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": null, "Address2": null, "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P2S6", "MoveLegType": "FPU", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": null, "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": null, "Address2": null, "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}]}]}]}