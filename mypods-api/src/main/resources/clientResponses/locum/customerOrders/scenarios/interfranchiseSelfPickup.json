{"CallInfo": {"StatusCode": "OK", "StatusMessage": "OK", "ServerUsed": "TPASTVWIWEB03", "ExceptionDetails": null}, "Response": [{"OrderId": "4865646", "OrderType": "Interfranchise", "QuoteId": "*********", "RentalAgreementAccepted": true, "InvasiveMothAgreementAccepted": false, "OrderDate": "2024-05-16T17:52:42.3137204", "QuoteChannelType": "Web", "Price": 2366.47, "Containers": [{"ContainerId": null, "ContainerType": "16-foot length container", "CompanyCode": "Z101", "HasCityServiceMoves": false, "MoveLegs": [{"MoveLegId": "*********P1S1", "MoveLegType": "NEW", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2024-09-16T00:00:00", "ETA": null, "TransitDays": 0, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "123 street", "Address2": null, "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "SchedulingRule": {"CanSchedule": true, "ScheduleStart": "2024-06-21T15:06:30.806982-04:00", "ScheduleEnd": "2024-09-23T00:00:00"}}, {"MoveLegId": "*********P1S2", "MoveLegType": "WRT", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2024-09-24T00:00:00", "ETA": null, "TransitDays": 1, "SiteIdentity": "ELP2", "OriginationAddress": {"Address1": "123 street", "Address2": null, "City": "El Paso", "State": "TX", "PostalCode": "79907", "Country": "US", "IsStorageCenter": false}, "DestinationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P1S4", "MoveLegType": "WTW", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2024-09-27T00:00:00", "ETA": null, "TransitDays": 7, "SiteIdentity": "OC15", "OriginationAddress": {"Address1": "50 <PERSON> Blvd", "Address2": "Suite 6", "City": "El Paso", "State": "TX", "PostalCode": "79906", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "7900 Nelson Road", "Address2": null, "City": "<PERSON>", "State": "CA", "PostalCode": "91402", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}, {"MoveLegId": "*********P1S5", "MoveLegType": "SFP", "MoveLegStatus": "Ordered", "IsCityService": false, "IsHawaii": false, "IsCrossBorder": false, "ScheduledDate": "2024-10-24T00:00:00", "ETA": "07:00 AM - 03:00 PM", "TransitDays": 0, "SiteIdentity": "OC15", "OriginationAddress": {"Address1": "7900 Nelson Road", "Address2": null, "City": "<PERSON>", "State": "CA", "PostalCode": "91402", "Country": "US", "IsStorageCenter": true}, "DestinationAddress": {"Address1": "7900 Nelson Road", "Address2": null, "City": "<PERSON>", "State": "CA", "PostalCode": "91402", "Country": "US", "IsStorageCenter": true}, "SchedulingRule": {"CanSchedule": false, "ScheduleStart": "0001-01-01T00:00:00", "ScheduleEnd": "0001-01-01T00:00:00"}}]}]}]}