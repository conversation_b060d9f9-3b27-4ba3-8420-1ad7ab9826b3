image:
  repository: podscreuspd.azurecr.io/mypods-api

replicaCount: 2

podLabels:
  admission.datadoghq.com/enabled: "true"
  tags.datadoghq.com/env: "pd"

resources:
  limits:
    memory: 1.5Gi
  requests:
    memory: 1.5Gi

ingress:
  annotations:
    appgw.ingress.kubernetes.io/request-timeout: "120"
    appgw.ingress.kubernetes.io/appgw-ssl-certificate: microservices-pods-com
    appgw.ingress.kubernetes.io/ssl-redirect: "true"
  waf:
    appgw.ingress.kubernetes.io/waf-policy-for-path: "/subscriptions/2a71f9dc-3c1b-48dd-bb6b-c3b9e9456fc1/resourceGroups/RG-SERVICES-PD-DR/providers/Microsoft.Network/ApplicationGatewayWebApplicationFirewallPolicies/WAF-AGW-WUS-SERVICES-PD-APIGEE-01"

env:
  variables:
    - name: SPRING_PROFILES_ACTIVE
      value: "prod"

secretStore:
  parameters:
    keyvaultName: KV-EUS-MYPODS-PD-01
    tenantId: 5eff806f-f468-4075-a2d2-8ec479fa58f3