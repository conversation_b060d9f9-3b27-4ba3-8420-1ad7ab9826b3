image:
  repository: podscreuste.azurecr.io/mypods-api

ingress:
  annotations:
    appgw.ingress.kubernetes.io/request-timeout: "120"
    appgw.ingress.kubernetes.io/appgw-ssl-certificate: dev-test-stage-microservices-pods-com
    appgw.ingress.kubernetes.io/ssl-redirect: "true"
  waf:
    appgw.ingress.kubernetes.io/waf-policy-for-path: "/subscriptions/805be479-ee7f-4c01-aed8-8267c4ff0664/resourceGroups/RG-SERVICES-TE/providers/Microsoft.Network/ApplicationGatewayWebApplicationFirewallPolicies/WAF-AGW-EUS-SERVICES-TE-APIGEE-01"
  agic:
    apigeeAppendedRoute: /mypods-bau

env:
  variables:
    - name: SPRING_PROFILES_ACTIVE
      value: "bau-test"

secretStore:
  parameters:
    keyvaultName: KV-EUS-MYPODS-TE-01
    tenantId: 5eff806f-f468-4075-a2d2-8ec479fa58f3