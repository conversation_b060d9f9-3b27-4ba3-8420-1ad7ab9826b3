image:
  repository: podscreusst.azurecr.io/mypods-api

replicaCount: 2

podLabels:
  admission.datadoghq.com/enabled: "true"
  tags.datadoghq.com/env: "st"

resources:
  limits:
    memory: 1.5Gi
  requests:
    memory: 1.5Gi

ingress:
  annotations:
    appgw.ingress.kubernetes.io/request-timeout: "120"
    appgw.ingress.kubernetes.io/appgw-ssl-certificate: dev-test-stage-microservices-pods-com
    appgw.ingress.kubernetes.io/ssl-redirect: "true"
  waf:
    appgw.ingress.kubernetes.io/waf-policy-for-path: "/subscriptions/805be479-ee7f-4c01-aed8-8267c4ff0664/resourceGroups/RG-SERVICES-ST-DR/providers/Microsoft.Network/applicationGatewayWebApplicationFirewallPolicies/WAF-AGW-WUS-SERVICES-ST-APIGEE-01"

env:
  variables:
    - name: SPRING_PROFILES_ACTIVE
      value: "stage"

secretStore:
  parameters:
    keyvaultName: KV-EUS-MYPODS-ST-01
    tenantId: 5eff806f-f468-4075-a2d2-8ec479fa58f3